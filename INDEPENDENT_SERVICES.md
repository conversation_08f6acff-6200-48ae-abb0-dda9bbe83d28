# 🚀 Independent AI Services

This document explains how to run each AI service independently for easier development, debugging, and deployment.

## 📋 Available Services

| Service | Port | Script | Description |
|---------|------|--------|-------------|
| 📊 Dashboard | 8000 | `start_dashboard.py` | Web dashboard for monitoring services |
| 🌐 Suna | 8001 | `start_suna.py` | Web automation and scraping |
| 🖥️ Agent-S | 8002 | `start_agent_s.py` | Desktop automation |
| 🎙️ LiveKit | 8003 | `start_livekit.py` | Real-time voice/video AI |
| 👥 CrewAI | 8004 | `start_crewai.py` | Multi-agent workflows |
| 🤖 CopilotKit | 3000 | `start_copilotkit.py` | Frontend AI assistance |

## 🎯 Quick Start

### Option 1: Using the Controller Script (Recommended)

```bash
# List all available services
python start_services.py --list

# Start the dashboard first
python start_services.py --service dashboard

# Start individual services (in separate terminals)
python start_services.py --service suna
python start_services.py --service agent-s
python start_services.py --service livekit
python start_services.py --service crewai
python start_services.py --service copilotkit
```

### Option 2: Direct Script Execution

```bash
# Start services directly
python start_dashboard.py
python start_suna.py
python start_agent_s.py
python start_livekit.py
python start_crewai.py
python start_copilotkit.py
```

### Option 3: Windows Batch Files

Double-click `start_dashboard.bat` to start the dashboard with a simple GUI.

## 🔧 Development Workflow

### 1. Start Dashboard First
```bash
python start_services.py --service dashboard
```
- Open http://localhost:8000 to see the service status dashboard
- This provides real-time monitoring of all services

### 2. Start Services as Needed
Start only the services you're working on:

```bash
# Working on web automation?
python start_services.py --service suna

# Working on desktop automation?
python start_services.py --service agent-s

# Working on voice AI?
python start_services.py --service livekit
```

### 3. Access Service Documentation
Each service provides its own API documentation:
- Dashboard: http://localhost:8000/docs
- Suna: http://localhost:8001/docs
- Agent-S: http://localhost:8002/docs
- LiveKit: http://localhost:8003/docs
- CrewAI: http://localhost:8004/docs
- CopilotKit: http://localhost:3000/docs

## 🛠️ Service Details

### 📊 Dashboard (Port 8000)
- **Purpose**: Monitor and control all AI services
- **Features**: Real-time status, service health checks, quick links
- **Dependencies**: None (runs independently)

### 🌐 Suna (Port 8001)
- **Purpose**: Web automation and scraping
- **Features**: Browser automation, web scraping, form filling
- **Dependencies**: OpenAI API key

### 🖥️ Agent-S (Port 8002)
- **Purpose**: Desktop automation
- **Features**: Screen capture, application control, UI automation
- **Dependencies**: Anthropic API key

### 🎙️ LiveKit (Port 8003)
- **Purpose**: Real-time voice and video AI
- **Features**: Voice processing, video analysis, real-time AI
- **Dependencies**: LiveKit credentials, OpenAI API key

### 👥 CrewAI (Port 8004)
- **Purpose**: Multi-agent workflows
- **Features**: Agent orchestration, workflow management, team collaboration
- **Dependencies**: OpenAI API key

### 🤖 CopilotKit (Port 3000)
- **Purpose**: Frontend AI assistance
- **Features**: Code suggestions, component generation, chat interface
- **Dependencies**: OpenAI API key

## 🔐 Configuration

Each service uses the same `.env` file for configuration. Key settings:

```env
# Enable/disable individual services
ENABLE_SUNA=true
ENABLE_AGENT_S=true
ENABLE_LIVEKIT=true
ENABLE_CREWAI=true
ENABLE_COPILOTKIT=true

# API Keys (required for respective services)
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
LIVEKIT_URL=your-livekit-url
LIVEKIT_API_KEY=your-livekit-key
LIVEKIT_API_SECRET=your-livekit-secret
```

## 🐛 Debugging

### Individual Service Logs
Each service runs independently, making debugging easier:
- Logs are isolated per service
- No interference between services
- Easy to restart individual services

### Health Checks
All services provide health check endpoints:
```bash
curl http://localhost:8000/health  # Dashboard
curl http://localhost:8001/health  # Suna
curl http://localhost:8002/health  # Agent-S
curl http://localhost:8003/health  # LiveKit
curl http://localhost:8004/health  # CrewAI
curl http://localhost:3000/health  # CopilotKit
```

### Common Issues

1. **Port Already in Use**
   - Check if another service is running on the same port
   - Use `netstat -an | findstr :8000` (Windows) or `lsof -i :8000` (Mac/Linux)

2. **Service Won't Start**
   - Check the `.env` file for required API keys
   - Verify Python dependencies are installed
   - Check the service logs for specific errors

3. **Service Shows as Offline in Dashboard**
   - Ensure the service is actually running
   - Check firewall settings
   - Verify the service is listening on the correct port

## 💡 Tips

1. **Use Multiple Terminals**: Start each service in its own terminal window for easier monitoring
2. **Start Dashboard First**: The dashboard helps you monitor other services
3. **Check Dependencies**: Ensure you have the required API keys for the services you want to use
4. **Development Mode**: Services run with detailed logging for easier debugging
5. **Graceful Shutdown**: Use Ctrl+C to stop services cleanly

## 🚀 Production Deployment

For production, consider:
1. Using process managers (PM2, systemd, etc.)
2. Setting up reverse proxy (nginx)
3. Configuring proper logging
4. Setting up monitoring and alerting
5. Using environment-specific configuration files
