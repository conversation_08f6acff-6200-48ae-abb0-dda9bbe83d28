#!/usr/bin/env python3
"""
API Routes for AI Platform Integration Hub

Defines all REST API endpoints:
- Task management (/api/tasks/)
- Service management (/api/services/)
- Health monitoring (/api/health/)
- User sessions (/api/sessions/)
- Metrics and analytics (/api/metrics/)
- System administration (/api/admin/)
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query, Path
from fastapi.responses import JSONResponse, StreamingResponse, HTMLResponse
from pydantic import BaseModel, Field

from ..models import (
    TaskRequest,
    TaskResponse,
    UserSession,
    ServiceHealth,
    TaskStatus,
    WebAutomationTask,
    DesktopAutomationTask,
    MultiAgentWorkflowTask
)
from ..main import IntegrationHub
from ..config import get_config

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api", tags=["AI Platform API"])

# Request/Response Models
class TaskCreateRequest(BaseModel):
    """Request model for creating a new task."""
    task_type: str = Field(..., description="Type of task to execute")
    description: str = Field(..., description="Task description")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Task parameters")
    priority: int = Field(default=1, description="Task priority (1-10)")
    requires_ui: bool = Field(default=False, description="Whether task requires UI interaction")
    requires_internet: bool = Field(default=False, description="Whether task requires internet access")
    timeout: Optional[int] = Field(default=None, description="Task timeout in seconds")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class TaskStatusResponse(BaseModel):
    """Response model for task status."""
    task_id: str
    status: TaskStatus
    progress: float
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    execution_time: Optional[float] = None

class ServiceRegistrationRequest(BaseModel):
    """Request model for service registration."""
    service_name: str = Field(..., description="Service name")
    service_type: str = Field(..., description="Service type")
    capabilities: List[str] = Field(..., description="Service capabilities")
    url: str = Field(..., description="Service URL")
    config: Dict[str, Any] = Field(default_factory=dict, description="Service configuration")
    priority: int = Field(default=1, description="Service priority")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Service metadata")

class SessionCreateRequest(BaseModel):
    """Request model for creating a user session."""
    user_id: str = Field(..., description="User ID")
    session_type: str = Field(default="interactive", description="Session type")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Session metadata")

# Dependency to get IntegrationHub instance
async def get_integration_hub() -> IntegrationHub:
    """Get the IntegrationHub instance."""
    # This would be injected by the main application
    # For now, we'll assume it's available globally
    from ..main import integration_hub
    return integration_hub

# Task Management Endpoints
@router.post("/tasks/", response_model=TaskStatusResponse)
async def create_task(
    request: TaskCreateRequest,
    background_tasks: BackgroundTasks,
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Create and execute a new task."""
    try:
        # Create task request
        task_request = TaskRequest(
            task_type=request.task_type,
            description=request.description,
            parameters=request.parameters,
            priority=request.priority,
            requires_ui=request.requires_ui,
            requires_internet=request.requires_internet,
            timeout=request.timeout,
            metadata=request.metadata
        )
        
        # Create user session (simplified)
        user_session = UserSession(
            user_id="api_user",
            session_type="api",
            preferences={},
            metadata={"source": "api"}
        )
        
        # Submit task for execution
        task_response = await hub.process_task(task_request, user_session)
        
        return TaskStatusResponse(
            task_id=task_response.task_id,
            status=task_response.status,
            progress=task_response.progress,
            result=task_response.result,
            error=task_response.error,
            created_at=task_response.created_at,
            updated_at=task_response.updated_at,
            execution_time=task_response.execution_time
        )
    
    except Exception as e:
        logger.error(f"❌ Failed to create task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(
    task_id: str = Path(..., description="Task ID"),
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Get status of a specific task."""
    try:
        task_status = await hub.get_task_status(task_id)
        
        if not task_status:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return TaskStatusResponse(
            task_id=task_status['task_id'],
            status=TaskStatus(task_status['status']),
            progress=task_status.get('progress', 0.0),
            result=task_status.get('result'),
            error=task_status.get('error'),
            created_at=datetime.fromisoformat(task_status['created_at']),
            updated_at=datetime.fromisoformat(task_status['updated_at']),
            execution_time=task_status.get('execution_time')
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get task status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tasks/", response_model=List[TaskStatusResponse])
async def list_tasks(
    status: Optional[str] = Query(None, description="Filter by task status"),
    limit: int = Query(50, description="Maximum number of tasks to return"),
    offset: int = Query(0, description="Number of tasks to skip"),
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """List tasks with optional filtering."""
    try:
        tasks = await hub.list_tasks(
            status=status,
            limit=limit,
            offset=offset
        )
        
        return [
            TaskStatusResponse(
                task_id=task['task_id'],
                status=TaskStatus(task['status']),
                progress=task.get('progress', 0.0),
                result=task.get('result'),
                error=task.get('error'),
                created_at=datetime.fromisoformat(task['created_at']),
                updated_at=datetime.fromisoformat(task['updated_at']),
                execution_time=task.get('execution_time')
            )
            for task in tasks
        ]
    
    except Exception as e:
        logger.error(f"❌ Failed to list tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/tasks/{task_id}")
async def cancel_task(
    task_id: str = Path(..., description="Task ID"),
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Cancel a running task."""
    try:
        success = await hub.cancel_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Task not found or cannot be cancelled")
        
        return {"message": "Task cancelled successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to cancel task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Service Management Endpoints
@router.get("/services/", response_model=Dict[str, Any])
async def list_services(
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """List all registered services."""
    try:
        services = await hub.service_registry.get_all_services()
        return services
    
    except Exception as e:
        logger.error(f"❌ Failed to list services: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/services/register")
async def register_service(
    request: ServiceRegistrationRequest,
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Register a new service."""
    try:
        success = await hub.service_registry.register_service(
            service_id=f"{request.service_type}_{request.service_name}",
            service_name=request.service_name,
            service_type=request.service_type,
            capabilities=request.capabilities,
            url=request.url,
            config=request.config,
            priority=request.priority,
            metadata=request.metadata
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to register service")
        
        return {"message": "Service registered successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to register service: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/services/{service_id}")
async def deregister_service(
    service_id: str = Path(..., description="Service ID"),
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Deregister a service."""
    try:
        success = await hub.service_registry.deregister_service(service_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Service not found")
        
        return {"message": "Service deregistered successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to deregister service: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/services/{service_id}/health", response_model=ServiceHealth)
async def get_service_health(
    service_id: str = Path(..., description="Service ID"),
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Get health status of a specific service."""
    try:
        service = await hub.service_registry.get_service(service_id)
        
        if not service:
            raise HTTPException(status_code=404, detail="Service not found")
        
        health = await service.get_health()
        return health
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get service health: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Health Monitoring Endpoints
@router.get("/health/")
async def get_system_health(
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Get overall system health."""
    try:
        health_status = await hub.get_health_status()
        return health_status
    
    except Exception as e:
        logger.error(f"❌ Failed to get system health: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health/services")
async def get_all_services_health(
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Get health status of all services."""
    try:
        services_health = {}
        services = await hub.service_registry.get_all_services()
        
        for service_id, service_info in services.items():
            if service_info.get('is_active', False):
                service = await hub.service_registry.get_service(service_id)
                if service:
                    health = await service.get_health()
                    services_health[service_id] = {
                        'service_name': service_info['service_name'],
                        'service_type': service_info['service_type'],
                        'health': health.dict()
                    }
        
        return services_health
    
    except Exception as e:
        logger.error(f"❌ Failed to get services health: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Session Management Endpoints
@router.post("/sessions/", response_model=Dict[str, Any])
async def create_session(
    request: SessionCreateRequest,
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Create a new user session."""
    try:
        session = UserSession(
            user_id=request.user_id,
            session_type=request.session_type,
            preferences=request.preferences,
            metadata=request.metadata
        )
        
        session_id = await hub.create_user_session(session)
        
        return {
            'session_id': session_id,
            'user_id': session.user_id,
            'session_type': session.session_type,
            'created_at': session.created_at.isoformat(),
            'expires_at': session.expires_at.isoformat()
        }
    
    except Exception as e:
        logger.error(f"❌ Failed to create session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions/{session_id}")
async def get_session(
    session_id: str = Path(..., description="Session ID"),
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Get session information."""
    try:
        session = await hub.get_user_session(session_id)
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {
            'session_id': session.session_id,
            'user_id': session.user_id,
            'session_type': session.session_type,
            'preferences': session.preferences,
            'metadata': session.metadata,
            'created_at': session.created_at.isoformat(),
            'expires_at': session.expires_at.isoformat(),
            'is_active': session.is_active
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: str = Path(..., description="Session ID"),
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Delete a user session."""
    try:
        success = await hub.delete_user_session(session_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {"message": "Session deleted successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to delete session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Metrics and Analytics Endpoints
@router.get("/metrics/")
async def get_system_metrics(
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Get system metrics and analytics."""
    try:
        metrics = await hub.get_system_metrics()
        return metrics
    
    except Exception as e:
        logger.error(f"❌ Failed to get system metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/metrics/services")
async def get_service_metrics(
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Get service-specific metrics."""
    try:
        service_metrics = await hub.service_registry.get_service_metrics()
        return service_metrics
    
    except Exception as e:
        logger.error(f"❌ Failed to get service metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/metrics/tasks")
async def get_task_metrics(
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Get task execution metrics."""
    try:
        task_metrics = await hub.get_task_metrics()
        return task_metrics
    
    except Exception as e:
        logger.error(f"❌ Failed to get task metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# System Administration Endpoints
@router.get("/admin/config")
async def get_system_config():
    """Get system configuration (sanitized)."""
    try:
        config = get_config()
        config_dict = config.to_dict()
        
        # Remove sensitive information
        if 'ai_models' in config_dict:
            config_dict['ai_models'] = {
                'default_model': config_dict['ai_models']['default_model'],
                'max_tokens': config_dict['ai_models']['max_tokens'],
                'temperature': config_dict['ai_models']['temperature']
            }
        
        if 'security' in config_dict:
            config_dict['security'] = {
                'jwt_algorithm': config_dict['security']['jwt_algorithm'],
                'jwt_expiration_hours': config_dict['security']['jwt_expiration_hours'],
                'api_rate_limit': config_dict['security']['api_rate_limit']
            }
        
        return config_dict
    
    except Exception as e:
        logger.error(f"❌ Failed to get system config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/restart")
async def restart_system(
    background_tasks: BackgroundTasks,
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Restart the system (graceful shutdown and restart)."""
    try:
        # Schedule restart in background
        background_tasks.add_task(hub.restart_system)
        
        return {"message": "System restart initiated"}
    
    except Exception as e:
        logger.error(f"❌ Failed to restart system: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/services/{service_id}/restart")
async def restart_service(
    background_tasks: BackgroundTasks,
    service_id: str = Path(..., description="Service ID"),
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Restart a specific service."""
    try:
        # Schedule service restart in background
        background_tasks.add_task(hub.restart_service, service_id)
        
        return {"message": f"Service {service_id} restart initiated"}
    
    except Exception as e:
        logger.error(f"❌ Failed to restart service: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket endpoint for real-time updates
@router.websocket("/ws/tasks/{task_id}")
async def websocket_task_updates(
    websocket,
    task_id: str = Path(..., description="Task ID"),
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """WebSocket endpoint for real-time task updates."""
    await websocket.accept()
    
    try:
        while True:
            # Get current task status
            task_status = await hub.get_task_status(task_id)
            
            if task_status:
                await websocket.send_json(task_status)
                
                # If task is completed, close connection
                if task_status.get('status') in ['completed', 'failed', 'cancelled']:
                    break
            
            # Wait before next update
            await asyncio.sleep(1)
    
    except Exception as e:
        logger.error(f"❌ WebSocket error for task {task_id}: {str(e)}")
    finally:
        await websocket.close()

@router.websocket("/ws/system/health")
async def websocket_system_health(
    websocket,
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """WebSocket endpoint for real-time system health updates."""
    await websocket.accept()
    
    try:
        while True:
            # Get current system health
            health_status = await hub.get_health_status()
            await websocket.send_json(health_status)
            
            # Wait before next update
            await asyncio.sleep(5)
    
    except Exception as e:
        logger.error(f"❌ WebSocket error for system health: {str(e)}")
    finally:
        await websocket.close()

# Specialized Task Endpoints
@router.post("/tasks/web-automation", response_model=TaskStatusResponse)
async def create_web_automation_task(
    task: WebAutomationTask,
    background_tasks: BackgroundTasks,
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Create a web automation task."""
    try:
        task_request = TaskRequest(
            task_type="web_automation",
            description=f"Web automation: {task.action}",
            parameters=task.dict(),
            requires_ui=True,
            requires_internet=True
        )
        
        user_session = UserSession(
            user_id="api_user",
            session_type="web_automation",
            preferences={},
            metadata={"source": "api", "task_type": "web_automation"}
        )
        
        task_response = await hub.process_task(task_request, user_session)
        
        return TaskStatusResponse(
            task_id=task_response.task_id,
            status=task_response.status,
            progress=task_response.progress,
            result=task_response.result,
            error=task_response.error,
            created_at=task_response.created_at,
            updated_at=task_response.updated_at,
            execution_time=task_response.execution_time
        )
    
    except Exception as e:
        logger.error(f"❌ Failed to create web automation task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks/desktop-automation", response_model=TaskStatusResponse)
async def create_desktop_automation_task(
    task: DesktopAutomationTask,
    background_tasks: BackgroundTasks,
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Create a desktop automation task."""
    try:
        task_request = TaskRequest(
            task_type="desktop_automation",
            description=f"Desktop automation: {task.action}",
            parameters=task.dict(),
            requires_ui=True
        )
        
        user_session = UserSession(
            user_id="api_user",
            session_type="desktop_automation",
            preferences={},
            metadata={"source": "api", "task_type": "desktop_automation"}
        )
        
        task_response = await hub.process_task(task_request, user_session)
        
        return TaskStatusResponse(
            task_id=task_response.task_id,
            status=task_response.status,
            progress=task_response.progress,
            result=task_response.result,
            error=task_response.error,
            created_at=task_response.created_at,
            updated_at=task_response.updated_at,
            execution_time=task_response.execution_time
        )
    
    except Exception as e:
        logger.error(f"❌ Failed to create desktop automation task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/tasks/multi-agent-workflow", response_model=TaskStatusResponse)
async def create_multi_agent_workflow_task(
    task: MultiAgentWorkflowTask,
    background_tasks: BackgroundTasks,
    hub: IntegrationHub = Depends(get_integration_hub)
):
    """Create a multi-agent workflow task."""
    try:
        task_request = TaskRequest(
            task_type="multi_agent_workflow",
            description=f"Multi-agent workflow: {task.workflow_name}",
            parameters=task.dict(),
            requires_internet=True
        )
        
        user_session = UserSession(
            user_id="api_user",
            session_type="multi_agent_workflow",
            preferences={},
            metadata={"source": "api", "task_type": "multi_agent_workflow"}
        )
        
        task_response = await hub.process_task(task_request, user_session)
        
        return TaskStatusResponse(
            task_id=task_response.task_id,
            status=task_response.status,
            progress=task_response.progress,
            result=task_response.result,
            error=task_response.error,
            created_at=task_response.created_at,
            updated_at=task_response.updated_at,
            execution_time=task_response.execution_time
        )
    
    except Exception as e:
        logger.error(f"❌ Failed to create multi-agent workflow task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Dashboard route is now handled in the main app.py file