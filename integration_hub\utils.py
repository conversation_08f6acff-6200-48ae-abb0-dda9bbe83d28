"""
Utility classes for the AI Platform Integration Hub.

This module provides utility classes for task routing, response aggregation,
error handling, and metrics collection.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone
from collections import defaultdict, deque
from dataclasses import dataclass, field

from .models import (
    TaskRequest,
    TaskResponse,
    TaskStatus,
    ServiceType,
    ErrorInfo
)

logger = logging.getLogger(__name__)


@dataclass
class RouteDecision:
    """Decision for routing a task to services."""
    services: List[str]
    execution_order: List[str]
    parallel_execution: bool = False
    confidence: float = 1.0
    reasoning: str = ""


class TaskRouter:
    """Routes tasks to appropriate services based on task type and requirements."""
    
    def __init__(self):
        self.routing_rules = {
            "web_automation": ["agent_s"],
            "desktop_automation": ["agent_s"],
            "voice_interaction": ["livekit"],
            "multi_agent_workflow": ["crewai"],
            "code_generation": ["copilotkit", "suna"],
            "content_generation": ["suna", "copilotkit"],
            "research_analysis": ["crewai", "suna"],
            "system_integration": ["suna"],
            "custom_workflow": ["crewai", "suna"]
        }
        
    async def route_task(self, task: TaskRequest) -> RouteDecision:
        """Route a task to appropriate services."""
        try:
            task_type = task.task_type.value if hasattr(task.task_type, 'value') else str(task.task_type)
            
            # Get primary services for this task type
            primary_services = self.routing_rules.get(task_type, ["suna"])
            
            # Determine execution strategy
            parallel_execution = len(primary_services) > 1 and task_type in [
                "content_generation", "research_analysis"
            ]
            
            execution_order = primary_services.copy()
            
            return RouteDecision(
                services=primary_services,
                execution_order=execution_order,
                parallel_execution=parallel_execution,
                confidence=0.9,
                reasoning=f"Routed {task_type} to {primary_services}"
            )
            
        except Exception as e:
            logger.error(f"Error routing task {task.task_id}: {e}")
            return RouteDecision(
                services=["suna"],  # Fallback to Suna
                execution_order=["suna"],
                confidence=0.5,
                reasoning=f"Fallback routing due to error: {e}"
            )


class ResponseAggregator:
    """Aggregates responses from multiple services."""
    
    def __init__(self):
        self.pending_responses: Dict[str, List[TaskResponse]] = defaultdict(list)
        
    async def add_response(self, task_id: str, response: TaskResponse) -> None:
        """Add a response for aggregation."""
        self.pending_responses[task_id].append(response)
        
    async def aggregate_responses(self, task_id: str, expected_count: int) -> TaskResponse:
        """Aggregate responses for a task."""
        responses = self.pending_responses.get(task_id, [])
        
        if len(responses) < expected_count:
            # Return partial aggregation
            return self._create_partial_response(task_id, responses)
            
        # Create final aggregated response
        return self._create_final_response(task_id, responses)
        
    def _create_partial_response(self, task_id: str, responses: List[TaskResponse]) -> TaskResponse:
        """Create a partial aggregated response."""
        if not responses:
            return TaskResponse(
                task_id=task_id,
                status=TaskStatus.RUNNING,
                message="Waiting for service responses",
                data={}
            )
            
        # Combine data from available responses
        combined_data = {}
        messages = []
        
        for response in responses:
            combined_data.update(response.data)
            messages.append(response.message)
            
        return TaskResponse(
            task_id=task_id,
            status=TaskStatus.RUNNING,
            message="; ".join(messages),
            data=combined_data
        )
        
    def _create_final_response(self, task_id: str, responses: List[TaskResponse]) -> TaskResponse:
        """Create the final aggregated response."""
        # Determine overall status
        statuses = [r.status for r in responses]
        if all(s == TaskStatus.COMPLETED for s in statuses):
            final_status = TaskStatus.COMPLETED
        elif any(s == TaskStatus.FAILED for s in statuses):
            final_status = TaskStatus.FAILED
        else:
            final_status = TaskStatus.RUNNING
            
        # Combine all data and messages
        combined_data = {}
        messages = []
        
        for response in responses:
            combined_data.update(response.data)
            messages.append(response.message)
            
        # Clean up
        if task_id in self.pending_responses:
            del self.pending_responses[task_id]
            
        return TaskResponse(
            task_id=task_id,
            status=final_status,
            message="; ".join(messages),
            data=combined_data
        )


class ErrorHandler:
    """Handles errors and provides recovery strategies."""
    
    def __init__(self):
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.error_history: deque = deque(maxlen=1000)
        
    async def handle_error(self, error: Exception, context: Dict[str, Any]) -> ErrorInfo:
        """Handle an error and return error information."""
        error_type = type(error).__name__
        error_message = str(error)
        
        # Track error
        self.error_counts[error_type] += 1
        
        error_info = ErrorInfo(
            error_type=error_type,
            message=error_message,
            context=context,
            timestamp=datetime.now(timezone.utc),
            severity="high" if error_type in ["ConnectionError", "TimeoutError"] else "medium"
        )
        
        self.error_history.append(error_info)
        logger.error(f"Error handled: {error_type} - {error_message}")
        
        return error_info
        
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        return {
            "total_errors": sum(self.error_counts.values()),
            "error_types": dict(self.error_counts),
            "recent_errors": len([e for e in self.error_history 
                                if (datetime.now(timezone.utc) - e.timestamp).seconds < 3600])
        }


@dataclass
class MetricData:
    """Container for metric data."""
    name: str
    value: float
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    tags: Dict[str, str] = field(default_factory=dict)


class MetricsCollector:
    """Collects and manages system metrics."""
    
    def __init__(self):
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.counters: Dict[str, int] = defaultdict(int)
        
    async def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None) -> None:
        """Record a metric value."""
        metric = MetricData(
            name=name,
            value=value,
            tags=tags or {}
        )
        self.metrics[name].append(metric)
        
    async def increment_counter(self, name: str, amount: int = 1) -> None:
        """Increment a counter metric."""
        self.counters[name] += amount
        
    async def record_timing(self, name: str, start_time: float) -> None:
        """Record timing metric."""
        duration = time.time() - start_time
        await self.record_metric(f"{name}_duration_ms", duration * 1000)
        
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of all metrics."""
        summary = {
            "counters": dict(self.counters),
            "metrics": {}
        }
        
        for name, values in self.metrics.items():
            if values:
                recent_values = [m.value for m in values if 
                               (datetime.now(timezone.utc) - m.timestamp).seconds < 3600]
                if recent_values:
                    summary["metrics"][name] = {
                        "count": len(recent_values),
                        "avg": sum(recent_values) / len(recent_values),
                        "min": min(recent_values),
                        "max": max(recent_values)
                    }
                    
        return summary
