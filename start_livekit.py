#!/usr/bin/env python3
"""
🎙️ LiveKit Voice/Video AI Service - Independent Startup
Starts only the LiveKit real-time voice and video AI service.
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from integration_hub.config import Config
from integration_hub.services.livekit_service import LiveKitService
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

def create_livekit_app() -> FastAPI:
    """Create FastAPI app for LiveKit service only."""
    app = FastAPI(
        title="🎙️ LiveKit Voice/Video AI Service",
        description="Independent real-time voice and video AI service",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    return app

async def main():
    """Main function to start LiveKit service."""
    print("🎙️ Starting LiveKit Voice/Video AI Service...")
    
    # Load configuration
    config = Config()
    
    # Create FastAPI app
    app = create_livekit_app()
    
    # Initialize LiveKit service with proper parameters
    livekit_service = LiveKitService(
        name="livekit",
        url="http://localhost:8003",
        capabilities=["voice_ai", "video_ai", "real_time_processing"]
    )
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize LiveKit service on startup."""
        try:
            await livekit_service.initialize()
            print("✅ LiveKit service initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize LiveKit service: {e}")
            sys.exit(1)
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup LiveKit service on shutdown."""
        try:
            await livekit_service.shutdown()
            print("✅ LiveKit service shutdown complete")
        except Exception as e:
            print(f"⚠️ Error during LiveKit shutdown: {e}")
    
    # Add LiveKit routes
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": "LiveKit Voice/Video AI",
            "status": "running",
            "version": "1.0.0",
            "docs": "/docs"
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        try:
            status = await livekit_service.health_check()
            return status
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    @app.post("/api/voice/session")
    async def create_voice_session(session_data: dict):
        """Create a voice AI session."""
        try:
            result = await livekit_service.create_voice_session(session_data)
            return result
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    @app.post("/api/video/session")
    async def create_video_session(session_data: dict):
        """Create a video AI session."""
        try:
            result = await livekit_service.create_video_session(session_data)
            return result
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    @app.get("/api/sessions")
    async def list_sessions():
        """List all active sessions."""
        try:
            sessions = await livekit_service.list_sessions()
            return {"sessions": sessions}
        except Exception as e:
            return {"error": str(e), "sessions": []}
    
    @app.get("/api/models/list")
    async def list_models():
        """List available AI models."""
        try:
            models = await livekit_service.list_models()
            return {"models": models}
        except Exception as e:
            return {"error": str(e), "models": []}
    
    # Start the server
    port = 8003  # Dedicated port for LiveKit
    print(f"🚀 Starting LiveKit service on http://localhost:{port}")
    print(f"📚 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    
    config_dict = {
        "app": app,
        "host": "0.0.0.0",
        "port": port,
        "reload": False,
        "log_level": "info"
    }
    
    await uvicorn.Server(uvicorn.Config(**config_dict)).serve()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 LiveKit service stopped by user")
    except Exception as e:
        print(f"❌ Failed to start LiveKit service: {e}")
        sys.exit(1)
