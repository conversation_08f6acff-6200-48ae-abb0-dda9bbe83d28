#!/usr/bin/env python3
"""
👥 CrewAI Multi-Agent Service - Independent Startup
Starts only the CrewAI multi-agent workflow service.
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from integration_hub.config import Config
from integration_hub.services.crewai_service import CrewAIService
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

def create_crewai_app() -> FastAPI:
    """Create FastAPI app for CrewAI service only."""
    app = FastAPI(
        title="👥 CrewAI Multi-Agent Service",
        description="Independent multi-agent workflow service",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    return app

async def main():
    """Main function to start CrewAI service."""
    print("👥 Starting CrewAI Multi-Agent Service...")
    
    # Load configuration
    config = Config()
    
    # Create FastAPI app
    app = create_crewai_app()
    
    # Initialize CrewAI service with proper parameters
    crewai_service = CrewAIService(
        name="crewai",
        url="http://localhost:8004",
        capabilities=["multi_agent", "workflows", "team_collaboration"]
    )
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize CrewAI service on startup."""
        try:
            await crewai_service.initialize()
            print("✅ CrewAI service initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize CrewAI service: {e}")
            sys.exit(1)
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup CrewAI service on shutdown."""
        try:
            await crewai_service.shutdown()
            print("✅ CrewAI service shutdown complete")
        except Exception as e:
            print(f"⚠️ Error during CrewAI shutdown: {e}")
    
    # Add CrewAI routes
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": "CrewAI Multi-Agent Workflows",
            "status": "running",
            "version": "1.0.0",
            "docs": "/docs"
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        try:
            status = await crewai_service.health_check()
            return status
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    @app.post("/api/workflows")
    async def create_workflow(workflow_data: dict):
        """Create a multi-agent workflow."""
        try:
            result = await crewai_service.create_workflow(workflow_data)
            return result
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    @app.get("/api/workflows")
    async def list_workflows():
        """List all workflows."""
        try:
            workflows = await crewai_service.list_workflows()
            return {"workflows": workflows}
        except Exception as e:
            return {"error": str(e), "workflows": []}
    
    @app.get("/api/agents/templates")
    async def list_agent_templates():
        """List available agent templates."""
        try:
            templates = await crewai_service.list_agent_templates()
            return {"templates": templates}
        except Exception as e:
            return {"error": str(e), "templates": []}
    
    @app.get("/api/tools/list")
    async def list_tools():
        """List available tools."""
        try:
            tools = await crewai_service.list_tools()
            return {"tools": tools}
        except Exception as e:
            return {"error": str(e), "tools": []}
    
    @app.post("/api/agents")
    async def create_agent(agent_data: dict):
        """Create a new agent."""
        try:
            result = await crewai_service.create_agent(agent_data)
            return result
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    # Start the server
    port = 8004  # Dedicated port for CrewAI
    print(f"🚀 Starting CrewAI service on http://localhost:{port}")
    print(f"📚 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    
    config_dict = {
        "app": app,
        "host": "0.0.0.0",
        "port": port,
        "reload": False,
        "log_level": "info"
    }
    
    await uvicorn.Server(uvicorn.Config(**config_dict)).serve()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 CrewAI service stopped by user")
    except Exception as e:
        print(f"❌ Failed to start CrewAI service: {e}")
        sys.exit(1)
