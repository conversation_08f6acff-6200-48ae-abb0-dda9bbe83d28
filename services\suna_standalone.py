"""
Standalone Suna Service Wrapper

This is a simplified version of Suna that can run without external dependencies
like Supabase, Redis, etc. It provides basic web automation capabilities.
"""

import os
import asyncio
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import uvicorn
import sys
from datetime import datetime, timezone

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Windows event loop policy
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

app = FastAPI(title="Suna Standalone Service", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class WebAutomationRequest(BaseModel):
    url: str
    action: str  # "scrape", "click", "fill", "navigate"
    selector: Optional[str] = None
    text: Optional[str] = None
    options: Optional[Dict[str, Any]] = {}

class WebAutomationResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    message: str
    timestamp: str

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    service: str
    version: str

# Mock web automation functions
async def mock_scrape_website(url: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
    """Mock website scraping function."""
    await asyncio.sleep(0.5)  # Simulate processing time
    
    return {
        "url": url,
        "title": f"Mock Title for {url}",
        "content": f"Mock content scraped from {url}",
        "links": [f"{url}/page1", f"{url}/page2"],
        "images": [f"{url}/image1.jpg", f"{url}/image2.jpg"],
        "metadata": {
            "scraped_at": datetime.now(timezone.utc).isoformat(),
            "word_count": 150,
            "load_time": "0.5s"
        }
    }

async def mock_click_element(url: str, selector: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
    """Mock element clicking function."""
    await asyncio.sleep(0.3)
    
    return {
        "url": url,
        "selector": selector,
        "action": "click",
        "result": "Element clicked successfully",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

async def mock_fill_form(url: str, selector: str, text: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
    """Mock form filling function."""
    await asyncio.sleep(0.4)
    
    return {
        "url": url,
        "selector": selector,
        "action": "fill",
        "text": text,
        "result": "Form field filled successfully",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

async def mock_navigate(url: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
    """Mock navigation function."""
    await asyncio.sleep(0.6)
    
    return {
        "url": url,
        "action": "navigate",
        "result": "Navigation completed successfully",
        "page_title": f"Mock Page Title for {url}",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

# API endpoints
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(timezone.utc).isoformat(),
        service="suna-standalone",
        version="1.0.0"
    )

@app.get("/api/status")
async def api_status():
    """API status endpoint for integration hub."""
    return {
        "status": "healthy",
        "service": "suna-standalone",
        "version": "1.0.0",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "capabilities": [
            "web_scraping",
            "element_clicking",
            "form_filling",
            "page_navigation"
        ]
    }

@app.post("/api/web-automation", response_model=WebAutomationResponse)
async def web_automation(request: WebAutomationRequest):
    """Main web automation endpoint."""
    try:
        logger.info(f"Processing web automation request: {request.action} on {request.url}")
        
        if request.action == "scrape":
            data = await mock_scrape_website(request.url, request.options)
        elif request.action == "click":
            if not request.selector:
                raise HTTPException(status_code=400, detail="Selector required for click action")
            data = await mock_click_element(request.url, request.selector, request.options)
        elif request.action == "fill":
            if not request.selector or not request.text:
                raise HTTPException(status_code=400, detail="Selector and text required for fill action")
            data = await mock_fill_form(request.url, request.selector, request.text, request.options)
        elif request.action == "navigate":
            data = await mock_navigate(request.url, request.options)
        else:
            raise HTTPException(status_code=400, detail=f"Unknown action: {request.action}")
        
        return WebAutomationResponse(
            success=True,
            data=data,
            message=f"Web automation '{request.action}' completed successfully",
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error in web automation: {str(e)}")
        return WebAutomationResponse(
            success=False,
            message=f"Error: {str(e)}",
            timestamp=datetime.now(timezone.utc).isoformat()
        )

@app.get("/api/capabilities")
async def get_capabilities():
    """Get service capabilities."""
    return {
        "service": "suna-standalone",
        "capabilities": [
            "web_scraping",
            "element_clicking", 
            "form_filling",
            "page_navigation"
        ],
        "supported_actions": ["scrape", "click", "fill", "navigate"],
        "version": "1.0.0",
        "status": "operational"
    }

@app.post("/api/scrape")
async def scrape_website(request: Dict[str, Any]):
    """Dedicated scraping endpoint."""
    url = request.get("url")
    if not url:
        raise HTTPException(status_code=400, detail="URL is required")
    
    options = request.get("options", {})
    data = await mock_scrape_website(url, options)
    
    return {
        "success": True,
        "data": data,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "Suna Standalone Service",
        "version": "1.0.0",
        "status": "running",
        "endpoints": [
            "/health",
            "/api/web-automation",
            "/api/capabilities", 
            "/api/scrape",
            "/docs"
        ],
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

if __name__ == "__main__":
    logger.info("Starting Suna Standalone Service on port 8001")
    uvicorn.run(
        "suna_standalone:app",
        host="0.0.0.0",
        port=8001,
        workers=1,
        loop="asyncio"
    )
