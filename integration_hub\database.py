"""
Database management module for the AI Platform Integration Hub.

This module provides database connectivity, schema management, and data
persistence functionality.
"""

import asyncio
import logging
import sqlite3
import aiosqlite
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone
from pathlib import Path

from .models import (
    TaskRequest,
    TaskResponse,
    UserSession,
    TaskStatus,
    ServiceHealth
)

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self, database_url: str = "sqlite:///./ai_platform.db"):
        self.database_url = database_url
        self.db_path = self._extract_db_path(database_url)
        self._connection_pool: Dict[str, Any] = {}
        
    def _extract_db_path(self, database_url: str) -> str:
        """Extract database path from URL."""
        if database_url.startswith("sqlite:///"):
            return database_url.replace("sqlite:///", "")
        elif database_url.startswith("sqlite://"):
            return database_url.replace("sqlite://", "")
        else:
            return "ai_platform.db"
            
    async def initialize(self) -> None:
        """Initialize the database and create tables."""
        try:
            await self._create_tables()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise
            
    async def _create_tables(self) -> None:
        """Create database tables."""
        async with aiosqlite.connect(self.db_path) as db:
            # Tasks table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS tasks (
                    task_id TEXT PRIMARY KEY,
                    task_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    status TEXT NOT NULL,
                    priority INTEGER DEFAULT 1,
                    parameters TEXT,
                    context TEXT,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Task responses table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS task_responses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id TEXT NOT NULL,
                    status TEXT NOT NULL,
                    message TEXT,
                    data TEXT,
                    execution_time_ms INTEGER,
                    services_used TEXT,
                    cost_estimate REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP,
                    FOREIGN KEY (task_id) REFERENCES tasks (task_id)
                )
            """)
            
            # User sessions table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    roles TEXT,
                    permissions TEXT,
                    preferences TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """)
            
            # Service health table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS service_health (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    service_name TEXT NOT NULL,
                    status TEXT NOT NULL,
                    response_time_ms REAL,
                    error_rate REAL,
                    last_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    details TEXT
                )
            """)
            
            # System metrics table
            await db.execute("""
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    tags TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            await db.commit()
            
    async def save_task(self, task: TaskRequest) -> bool:
        """Save a task to the database."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT OR REPLACE INTO tasks 
                    (task_id, task_type, title, description, status, priority, 
                     parameters, context, metadata, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task.task_id,
                    str(task.task_type),
                    task.title,
                    task.description,
                    "pending",
                    task.priority,
                    str(task.parameters),
                    str(task.context),
                    str(task.metadata),
                    task.created_at.isoformat(),
                    datetime.now(timezone.utc).isoformat()
                ))
                await db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error saving task {task.task_id}: {e}")
            return False
            
    async def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get a task from the database."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                async with db.execute(
                    "SELECT * FROM tasks WHERE task_id = ?", (task_id,)
                ) as cursor:
                    row = await cursor.fetchone()
                    if row:
                        return dict(row)
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting task {task_id}: {e}")
            return None
            
    async def update_task_status(self, task_id: str, status: str) -> bool:
        """Update task status."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    UPDATE tasks 
                    SET status = ?, updated_at = ?
                    WHERE task_id = ?
                """, (status, datetime.now(timezone.utc).isoformat(), task_id))
                await db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error updating task status {task_id}: {e}")
            return False
            
    async def save_task_response(self, response: TaskResponse) -> bool:
        """Save a task response to the database."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT INTO task_responses 
                    (task_id, status, message, data, execution_time_ms, 
                     services_used, cost_estimate, created_at, completed_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    response.task_id,
                    str(response.status),
                    response.message,
                    str(response.data),
                    response.execution_time_ms,
                    str(response.services_used),
                    response.cost_estimate,
                    response.created_at.isoformat(),
                    response.completed_at.isoformat() if response.completed_at else None
                ))
                await db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error saving task response {response.task_id}: {e}")
            return False
            
    async def get_task_responses(self, task_id: str) -> List[Dict[str, Any]]:
        """Get all responses for a task."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                async with db.execute(
                    "SELECT * FROM task_responses WHERE task_id = ? ORDER BY created_at",
                    (task_id,)
                ) as cursor:
                    rows = await cursor.fetchall()
                    return [dict(row) for row in rows]
                    
        except Exception as e:
            logger.error(f"Error getting task responses {task_id}: {e}")
            return []
            
    async def save_user_session(self, session: UserSession) -> bool:
        """Save a user session to the database."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT OR REPLACE INTO user_sessions 
                    (session_id, user_id, roles, permissions, preferences,
                     created_at, last_activity, expires_at, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    session.session_id,
                    session.user_id,
                    str(session.roles),
                    str(session.permissions),
                    str(session.preferences),
                    session.created_at.isoformat(),
                    session.last_activity.isoformat(),
                    session.expires_at.isoformat() if session.expires_at else None,
                    session.is_active
                ))
                await db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error saving user session {session.session_id}: {e}")
            return False
            
    async def get_user_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a user session from the database."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                async with db.execute(
                    "SELECT * FROM user_sessions WHERE session_id = ?", (session_id,)
                ) as cursor:
                    row = await cursor.fetchone()
                    if row:
                        return dict(row)
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting user session {session_id}: {e}")
            return None
            
    async def save_service_health(self, health: ServiceHealth) -> bool:
        """Save service health data."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT INTO service_health 
                    (service_name, status, response_time_ms, error_rate, last_check, details)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    health.service_name,
                    str(health.status),
                    health.response_time_ms,
                    health.error_rate,
                    health.last_check.isoformat(),
                    str(health.details)
                ))
                await db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error saving service health {health.service_name}: {e}")
            return False
            
    async def get_recent_tasks(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent tasks."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                async with db.execute(
                    "SELECT * FROM tasks ORDER BY created_at DESC LIMIT ?", (limit,)
                ) as cursor:
                    rows = await cursor.fetchall()
                    return [dict(row) for row in rows]
                    
        except Exception as e:
            logger.error(f"Error getting recent tasks: {e}")
            return []
            
    async def cleanup_old_data(self, days: int = 30) -> int:
        """Clean up old data and return count of removed records."""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            async with aiosqlite.connect(self.db_path) as db:
                # Clean up old task responses
                cursor = await db.execute(
                    "DELETE FROM task_responses WHERE created_at < ?",
                    (cutoff_date.isoformat(),)
                )
                responses_deleted = cursor.rowcount
                
                # Clean up old service health records
                cursor = await db.execute(
                    "DELETE FROM service_health WHERE last_check < ?",
                    (cutoff_date.isoformat(),)
                )
                health_deleted = cursor.rowcount
                
                # Clean up old metrics
                cursor = await db.execute(
                    "DELETE FROM system_metrics WHERE timestamp < ?",
                    (cutoff_date.isoformat(),)
                )
                metrics_deleted = cursor.rowcount
                
                await db.commit()
                
                total_deleted = responses_deleted + health_deleted + metrics_deleted
                logger.info(f"Cleaned up {total_deleted} old records")
                return total_deleted
                
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            return 0
            
    async def close(self) -> None:
        """Close database connections."""
        # Close any open connections in the pool
        for connection in self._connection_pool.values():
            if hasattr(connection, 'close'):
                await connection.close()
        self._connection_pool.clear()
        logger.info("Database connections closed")
