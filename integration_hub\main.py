#!/usr/bin/env python3
"""
AI Platform Integration Hub

Main orchestration service that coordinates all AI systems:
- CopilotKit (UI/UX)
- <PERSON><PERSON> (Web automation)
- Agent-S (Desktop automation)
- LiveKit Agents (Voice interface)
- <PERSON><PERSON><PERSON> (Multi-agent orchestration)
"""

import asyncio
import logging
import os
import sys
import time
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
import redis.asyncio as redis
import asyncpg
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our custom modules
from .services import (
    SunaService,
    AgentSService,
    LiveKitService,
    CrewAIService,
    CopilotKitService
)
from .models import (
    TaskRequest,
    TaskResponse,
    TaskStatus,
    ServiceHealth,
    UserSession
)
from .utils import (
    TaskRouter,
    ResponseAggregator,
    ErrorHandler,
    MetricsCollector
)
from .auth import AuthManager, AuthConfig
from .database import DatabaseManager

class IntegrationHub:
    """Main integration hub that orchestrates all AI services."""
    
    def __init__(self):
        self.services = {}
        self.redis_client = None
        self.db_manager = None

        # Initialize auth manager with config
        auth_config = AuthConfig(
            secret_key=os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production-12345678901234567890"),
            access_token_expire_minutes=int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
        )
        self.auth_manager = AuthManager(auth_config)
        self.task_router = TaskRouter()
        self.response_aggregator = ResponseAggregator()
        self.error_handler = ErrorHandler()
        self.metrics = MetricsCollector()
        
    async def initialize(self):
        """Initialize all services and connections."""
        logger.info("🚀 Initializing AI Platform Integration Hub")

        # Record start time for uptime calculation
        self._start_time = time.time()
        
        try:
            # Initialize Redis connection
            self.redis_client = redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD'),
                decode_responses=True
            )
            await self.redis_client.ping()
            logger.info("✅ Redis connection established")
            
            # Initialize database
            database_url = os.getenv('DATABASE_URL', 'sqlite:///./ai_platform.db')
            self.db_manager = DatabaseManager(database_url)
            await self.db_manager.initialize()
            logger.info("✅ Database connection established")
            
            # Initialize services
            await self._initialize_services()
            
            # Start background tasks
            asyncio.create_task(self._health_check_loop())
            asyncio.create_task(self._metrics_collection_loop())
            
            logger.info("🎉 Integration Hub initialized successfully")
            
        except Exception as e:
            logger.error(f"💥 Failed to initialize Integration Hub: {str(e)}")
            raise
    
    async def _initialize_services(self):
        """Initialize all AI services."""
        logger.info("🔧 Initializing AI services...")
        
        service_configs = {
            'suna': {
                'class': SunaService,
                'url': f"http://localhost:{os.getenv('SUNA_PORT', 8001)}",
                'capabilities': ['web_automation', 'browser_control', 'data_extraction']
            },
            'agent_s': {
                'class': AgentSService,
                'url': f"http://localhost:{os.getenv('AGENT_S_PORT', 8002)}",
                'capabilities': ['desktop_automation', 'gui_control', 'app_interaction']
            },
            'livekit': {
                'class': LiveKitService,
                'url': f"http://localhost:{os.getenv('LIVEKIT_PORT', 8003)}",
                'capabilities': ['voice_processing', 'speech_to_text', 'text_to_speech']
            },
            'crewai': {
                'class': CrewAIService,
                'url': f"http://localhost:{os.getenv('CREWAI_PORT', 8004)}",
                'capabilities': ['multi_agent_orchestration', 'task_delegation', 'workflow_management']
            },
            'copilotkit': {
                'class': CopilotKitService,
                'url': f"http://localhost:{os.getenv('COPILOTKIT_PORT', 3000)}",
                'capabilities': ['ui_integration', 'user_interaction', 'frontend_coordination']
            }
        }
        
        for service_name, config in service_configs.items():
            try:
                service_instance = config['class'](
                    name=service_name,
                    url=config['url'],
                    capabilities=config['capabilities'],
                    redis_client=self.redis_client
                )
                await service_instance.initialize()
                self.services[service_name] = service_instance
                logger.info(f"✅ {service_name} service initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize {service_name}: {str(e)}")
                # Continue with other services
    
    async def _health_check_loop(self):
        """Continuously monitor service health."""
        while True:
            try:
                for service_name, service in self.services.items():
                    health = await service.health_check()
                    await self.redis_client.setex(
                        f"health:{service_name}",
                        60,  # 1 minute TTL
                        health.json()
                    )
                    
                    if not health.is_healthy:
                        logger.warning(f"⚠️ Service {service_name} is unhealthy: {health.message}")
                        # Attempt to restart service
                        await self._restart_service(service_name)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"💥 Health check loop error: {str(e)}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _metrics_collection_loop(self):
        """Collect and store metrics from all services."""
        while True:
            try:
                metrics_data = await self.metrics.collect_all_metrics(self.services)
                await self.db_manager.store_metrics(metrics_data)
                await asyncio.sleep(60)  # Collect every minute
            except Exception as e:
                logger.error(f"💥 Metrics collection error: {str(e)}")
                await asyncio.sleep(120)  # Wait longer on error
    
    async def _restart_service(self, service_name: str):
        """Attempt to restart a failed service."""
        try:
            logger.info(f"🔄 Attempting to restart {service_name}")
            service = self.services[service_name]
            await service.restart()
            logger.info(f"✅ {service_name} restarted successfully")
        except Exception as e:
            logger.error(f"❌ Failed to restart {service_name}: {str(e)}")
    
    async def process_task(self, task_request: TaskRequest, user_session: UserSession) -> TaskResponse:
        """Process a task request through the appropriate services."""
        try:
            # Log the incoming request
            logger.info(f"📥 Processing task: {task_request.task_type} for user {user_session.user_id}")
            
            # Route the task to appropriate services
            routing_plan = await self.task_router.route_task(task_request)
            
            if not routing_plan.services:
                raise HTTPException(status_code=400, detail="No suitable services found for this task")
            
            # Execute the task across services
            service_responses = []
            for service_name in routing_plan.services:
                if service_name in self.services:
                    service = self.services[service_name]
                    try:
                        response = await service.execute_task(task_request, user_session)
                        service_responses.append(response)
                    except Exception as e:
                        logger.error(f"❌ Service {service_name} failed: {str(e)}")
                        # Continue with other services or handle gracefully
                        error_response = await self.error_handler.handle_service_error(
                            service_name, e, task_request
                        )
                        service_responses.append(error_response)
            
            # Aggregate responses
            final_response = await self.response_aggregator.aggregate(
                service_responses, task_request
            )
            
            # Store task execution record
            await self.db_manager.store_task_execution(
                task_request, final_response, user_session
            )
            
            # Update metrics
            await self.metrics.record_task_completion(
                task_request.task_type, final_response.status, len(service_responses)
            )
            
            logger.info(f"✅ Task completed: {task_request.task_id}")
            return final_response
            
        except Exception as e:
            logger.error(f"💥 Task processing failed: {str(e)}")
            error_response = await self.error_handler.handle_task_error(task_request, e)
            return error_response
    
    async def get_service_status(self) -> Dict[str, ServiceHealth]:
        """Get the current status of all services."""
        status = {}
        for service_name, service in self.services.items():
            try:
                health = await service.health_check()
                status[service_name] = health
            except Exception as e:
                status[service_name] = ServiceHealth(
                    service_name=service_name,
                    is_healthy=False,
                    message=f"Health check failed: {str(e)}",
                    last_check=None
                )
        return status

    async def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status."""
        try:
            # Get service statuses
            service_statuses = await self.get_service_status()

            # Count healthy services
            total_services = len(service_statuses)
            healthy_services = sum(1 for status in service_statuses.values() if status.status == "healthy")

            # Determine overall health
            overall_status = "healthy" if healthy_services == total_services else "degraded" if healthy_services > 0 else "unhealthy"

            # Get system metrics
            try:
                import psutil
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')

                system_metrics = {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_percent": disk.percent,
                    "memory_available_gb": round(memory.available / (1024**3), 2),
                    "disk_free_gb": round(disk.free / (1024**3), 2)
                }
            except Exception as e:
                logger.warning(f"Failed to get system metrics: {e}")
                system_metrics = {"error": "Unable to collect system metrics"}

            return {
                "status": overall_status,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "services": {
                    "total": total_services,
                    "healthy": healthy_services,
                    "unhealthy": total_services - healthy_services,
                    "details": service_statuses
                },
                "system": system_metrics,
                "uptime_seconds": time.time() - getattr(self, '_start_time', time.time()),
                "version": "1.0.0"
            }

        except Exception as e:
            logger.error(f"Failed to get health status: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    async def shutdown(self):
        """Gracefully shutdown all services."""
        logger.info("🛑 Shutting down Integration Hub")
        
        # Shutdown services
        for service_name, service in self.services.items():
            try:
                await service.shutdown()
                logger.info(f"✅ {service_name} shutdown complete")
            except Exception as e:
                logger.error(f"❌ Error shutting down {service_name}: {str(e)}")
        
        # Close connections
        if self.redis_client:
            await self.redis_client.close()
        
        if self.db_manager:
            await self.db_manager.close()
        
        logger.info("✅ Integration Hub shutdown complete")

# Global hub instance
hub = IntegrationHub()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    # Startup
    await hub.initialize()
    yield
    # Shutdown
    await hub.shutdown()

# Create FastAPI app
app = FastAPI(
    title="AI Platform Integration Hub",
    description="Central orchestration service for the integrated AI platform",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserSession:
    """Validate user authentication and return session."""
    try:
        user_session = await hub.auth_manager.validate_token(credentials.credentials)
        return user_session
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid authentication")

# API Routes

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    service_status = await hub.get_service_status()
    overall_health = all(status.is_healthy for status in service_status.values())
    
    return {
        "status": "healthy" if overall_health else "degraded",
        "services": service_status,
        "timestamp": asyncio.get_event_loop().time()
    }

@app.post("/tasks", response_model=TaskResponse)
async def create_task(
    task_request: TaskRequest,
    background_tasks: BackgroundTasks,
    user_session: UserSession = Depends(get_current_user)
):
    """Create and execute a new task."""
    try:
        # For long-running tasks, process in background
        if task_request.is_long_running:
            background_tasks.add_task(hub.process_task, task_request, user_session)
            return TaskResponse(
                task_id=task_request.task_id,
                status=TaskStatus.QUEUED,
                message="Task queued for background processing",
                data={}
            )
        else:
            # Process immediately for quick tasks
            return await hub.process_task(task_request, user_session)
    
    except Exception as e:
        logger.error(f"💥 Task creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task_status(
    task_id: str,
    user_session: UserSession = Depends(get_current_user)
):
    """Get the status of a specific task."""
    try:
        task_status = await hub.db_manager.get_task_status(task_id, user_session.user_id)
        if not task_status:
            raise HTTPException(status_code=404, detail="Task not found")
        return task_status
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 Failed to get task status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/services/status")
async def get_services_status(
    user_session: UserSession = Depends(get_current_user)
):
    """Get the status of all services."""
    return await hub.get_service_status()

@app.get("/metrics")
async def get_metrics(
    user_session: UserSession = Depends(get_current_user)
):
    """Get platform metrics."""
    try:
        metrics = await hub.metrics.get_current_metrics()
        return metrics
    except Exception as e:
        logger.error(f"💥 Failed to get metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/voice/session")
async def create_voice_session(
    user_session: UserSession = Depends(get_current_user)
):
    """Create a new voice interaction session."""
    try:
        if 'livekit' not in hub.services:
            raise HTTPException(status_code=503, detail="Voice service unavailable")
        
        voice_session = await hub.services['livekit'].create_session(user_session)
        return voice_session
    except Exception as e:
        logger.error(f"💥 Failed to create voice session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    # Run the server
    port = int(os.getenv('INTEGRATION_HUB_PORT', 8000))
    
    logger.info(f"🚀 Starting AI Platform Integration Hub on port {port}")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=os.getenv('DEBUG', 'false').lower() == 'true',
        log_level=os.getenv('LOG_LEVEL', 'info').lower()
    )