#!/usr/bin/env python3
"""
Data models for the AI Platform Integration Hub

Defines all the data structures used across the platform:
- Task requests and responses
- Service health and status
- User sessions and authentication
- Metrics and monitoring data
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from uuid import uuid4

from pydantic import BaseModel, Field, validator

class TaskType(str, Enum):
    """Types of tasks that can be processed by the platform."""
    WEB_AUTOMATION = "web_automation"
    DESKTOP_AUTOMATION = "desktop_automation"
    VOICE_INTERACTION = "voice_interaction"
    MULTI_AGENT_WORKFLOW = "multi_agent_workflow"
    DATA_EXTRACTION = "data_extraction"
    CONTENT_GENERATION = "content_generation"
    RESEARCH_ANALYSIS = "research_analysis"
    SYSTEM_INTEGRATION = "system_integration"
    CUSTOM_WORKFLOW = "custom_workflow"

class TaskStatus(str, Enum):
    """Status of task execution."""
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"

class Priority(str, Enum):
    """Task priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class ServiceType(str, Enum):
    """Types of services in the platform."""
    SUNA = "suna"
    AGENT_S = "agent_s"
    LIVEKIT = "livekit"
    CREWAI = "crewai"
    COPILOTKIT = "copilotkit"

class TaskRequest(BaseModel):
    """Request model for task execution."""
    task_id: str = Field(default_factory=lambda: str(uuid4()))
    task_type: TaskType
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1, max_length=2000)
    parameters: Dict[str, Any] = Field(default_factory=dict)
    priority: Priority = Priority.NORMAL
    timeout_seconds: Optional[int] = Field(default=300, ge=1, le=3600)
    is_long_running: bool = False
    requires_voice: bool = False
    requires_web: bool = False
    requires_desktop: bool = False
    context: Dict[str, Any] = Field(default_factory=dict)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    @validator('parameters')
    def validate_parameters(cls, v):
        """Ensure parameters are serializable."""
        if not isinstance(v, dict):
            raise ValueError("Parameters must be a dictionary")
        return v
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class TaskResponse(BaseModel):
    """Response model for task execution."""
    task_id: str
    status: TaskStatus
    message: str
    data: Dict[str, Any] = Field(default_factory=dict)
    results: List[Dict[str, Any]] = Field(default_factory=list)
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    execution_time_ms: Optional[int] = None
    services_used: List[str] = Field(default_factory=list)
    cost_estimate: Optional[float] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ServiceHealth(BaseModel):
    """Health status of a service."""
    service_name: str
    service_type: Optional[ServiceType] = None
    is_healthy: bool
    status_code: Optional[int] = None
    message: str = ""
    response_time_ms: Optional[float] = None
    last_check: Optional[datetime] = None
    uptime_seconds: Optional[int] = None
    version: Optional[str] = None
    capabilities: List[str] = Field(default_factory=list)
    metrics: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class UserSession(BaseModel):
    """User session information."""
    user_id: str
    session_id: str = Field(default_factory=lambda: str(uuid4()))
    username: Optional[str] = None
    email: Optional[str] = None
    roles: List[str] = Field(default_factory=list)
    permissions: List[str] = Field(default_factory=list)
    preferences: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    is_active: bool = True
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ServiceCapability(BaseModel):
    """Capability definition for a service."""
    name: str
    description: str
    input_schema: Dict[str, Any] = Field(default_factory=dict)
    output_schema: Dict[str, Any] = Field(default_factory=dict)
    cost_per_execution: Optional[float] = None
    average_execution_time_ms: Optional[int] = None
    reliability_score: Optional[float] = Field(default=1.0, ge=0.0, le=1.0)
    
class ServiceConfiguration(BaseModel):
    """Configuration for a service."""
    service_name: str
    service_type: ServiceType
    url: str
    api_key: Optional[str] = None
    timeout_seconds: int = Field(default=30, ge=1, le=300)
    max_retries: int = Field(default=3, ge=0, le=10)
    retry_delay_seconds: int = Field(default=1, ge=0, le=60)
    health_check_interval_seconds: int = Field(default=30, ge=5, le=300)
    capabilities: List[ServiceCapability] = Field(default_factory=list)
    resource_limits: Dict[str, Any] = Field(default_factory=dict)
    environment_variables: Dict[str, str] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True

class TaskRoutingPlan(BaseModel):
    """Plan for routing a task to services."""
    task_id: str
    services: List[str]
    execution_order: List[str] = Field(default_factory=list)
    parallel_execution: bool = False
    estimated_cost: Optional[float] = None
    estimated_duration_ms: Optional[int] = None
    confidence_score: float = Field(default=1.0, ge=0.0, le=1.0)
    reasoning: str = ""
    fallback_services: List[str] = Field(default_factory=list)
    
class MetricsData(BaseModel):
    """Metrics data structure."""
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    service_name: Optional[str] = None
    metric_type: str
    metric_name: str
    value: Union[int, float, str]
    unit: Optional[str] = None
    tags: Dict[str, str] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class PlatformMetrics(BaseModel):
    """Overall platform metrics."""
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    total_tasks_processed: int = 0
    active_tasks: int = 0
    failed_tasks_last_hour: int = 0
    average_response_time_ms: float = 0.0
    service_availability: Dict[str, float] = Field(default_factory=dict)
    resource_usage: Dict[str, Any] = Field(default_factory=dict)
    cost_metrics: Dict[str, float] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class VoiceSession(BaseModel):
    """Voice interaction session."""
    session_id: str = Field(default_factory=lambda: str(uuid4()))
    user_id: str
    room_name: str
    token: str
    websocket_url: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    is_active: bool = True
    language: str = "en-US"
    voice_settings: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class WebAutomationTask(BaseModel):
    """Specific task model for web automation."""
    url: str
    actions: List[Dict[str, Any]]
    wait_conditions: List[Dict[str, Any]] = Field(default_factory=list)
    data_extraction: Dict[str, Any] = Field(default_factory=dict)
    screenshot_required: bool = False
    headless: bool = True
    browser_type: str = "chromium"
    viewport: Dict[str, int] = Field(default_factory=lambda: {"width": 1920, "height": 1080})
    
class DesktopAutomationTask(BaseModel):
    """Specific task model for desktop automation."""
    application: str
    actions: List[Dict[str, Any]]
    coordinates: List[Dict[str, int]] = Field(default_factory=list)
    text_inputs: List[str] = Field(default_factory=list)
    screenshot_before: bool = False
    screenshot_after: bool = False
    wait_time_ms: int = Field(default=1000, ge=0, le=10000)
    
class MultiAgentWorkflow(BaseModel):
    """Multi-agent workflow definition."""
    workflow_name: str
    agents: List[Dict[str, Any]]
    tasks: List[Dict[str, Any]]
    dependencies: Dict[str, List[str]] = Field(default_factory=dict)
    execution_strategy: str = "sequential"  # sequential, parallel, conditional
    max_iterations: int = Field(default=10, ge=1, le=100)
    success_criteria: Dict[str, Any] = Field(default_factory=dict)

class MultiAgentWorkflowTask(BaseModel):
    """Specific task model for multi-agent workflows."""
    workflow_name: str
    agents: List[Dict[str, Any]]
    tasks: List[Dict[str, Any]]
    crew_config: Dict[str, Any] = Field(default_factory=dict)
    execution_mode: str = "sequential"  # sequential, parallel, hierarchical
    max_iterations: int = 5
    memory_enabled: bool = True
    verbose: bool = True
    
class ErrorInfo(BaseModel):
    """Error information structure."""
    error_id: str = Field(default_factory=lambda: str(uuid4()))
    error_type: str
    error_code: Optional[str] = None
    message: str
    details: Dict[str, Any] = Field(default_factory=dict)
    service_name: Optional[str] = None
    task_id: Optional[str] = None
    user_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    stack_trace: Optional[str] = None
    is_recoverable: bool = True
    retry_count: int = 0
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class SystemConfiguration(BaseModel):
    """System-wide configuration."""
    platform_name: str = "AI Platform Integration Hub"
    version: str = "1.0.0"
    environment: str = "development"  # development, staging, production
    debug_mode: bool = False
    max_concurrent_tasks: int = Field(default=100, ge=1, le=1000)
    default_timeout_seconds: int = Field(default=300, ge=1, le=3600)
    rate_limiting: Dict[str, int] = Field(default_factory=dict)
    security_settings: Dict[str, Any] = Field(default_factory=dict)
    monitoring_settings: Dict[str, Any] = Field(default_factory=dict)
    
class APIResponse(BaseModel):
    """Standard API response wrapper."""
    success: bool
    message: str = ""
    data: Optional[Any] = None
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: str = Field(default_factory=lambda: str(uuid4()))
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Utility functions for model validation and conversion

def validate_task_request(task_data: Dict[str, Any]) -> TaskRequest:
    """Validate and convert task data to TaskRequest model."""
    try:
        return TaskRequest(**task_data)
    except Exception as e:
        raise ValueError(f"Invalid task request: {str(e)}")

def create_error_response(error: Exception, task_id: Optional[str] = None) -> TaskResponse:
    """Create a standardized error response."""
    return TaskResponse(
        task_id=task_id or str(uuid4()),
        status=TaskStatus.FAILED,
        message=f"Task failed: {str(error)}",
        errors=[str(error)],
        completed_at=datetime.utcnow()
    )

def create_success_response(task_id: str, data: Dict[str, Any], message: str = "Task completed successfully") -> TaskResponse:
    """Create a standardized success response."""
    return TaskResponse(
        task_id=task_id,
        status=TaskStatus.COMPLETED,
        message=message,
        data=data,
        completed_at=datetime.utcnow()
    )

# Export all models
__all__ = [
    'TaskType', 'TaskStatus', 'Priority', 'ServiceType',
    'TaskRequest', 'TaskResponse', 'ServiceHealth', 'UserSession',
    'ServiceCapability', 'ServiceConfiguration', 'TaskRoutingPlan',
    'MetricsData', 'PlatformMetrics', 'VoiceSession',
    'WebAutomationTask', 'DesktopAutomationTask', 'MultiAgentWorkflow',
    'ErrorInfo', 'SystemConfiguration', 'APIResponse',
    'validate_task_request', 'create_error_response', 'create_success_response'
]