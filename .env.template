# =============================================================================
# AI PLATFORM INTEGRATION HUB - ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual API keys
# Get API keys from the providers listed below

# =============================================================================
# 🔐 SECURITY & AUTHENTICATION
# =============================================================================
SECRET_KEY=your-super-secret-key-change-this-in-production-min-32-chars
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production-min-32-chars
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Default admin credentials (CHANGE THESE!)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# =============================================================================
# 🤖 AI SERVICE API KEYS (REQUIRED)
# =============================================================================

# OpenAI API Key (REQUIRED for most services)
# Get from: https://platform.openai.com/api-keys
# Cost: Pay-per-use, starts at $0.002/1K tokens
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# Anthropic Claude API Key (REQUIRED for Agent-S and advanced features)
# Get from: https://console.anthropic.com/
# Cost: Pay-per-use, competitive pricing
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# =============================================================================
# 🌐 OPTIONAL AI SERVICE API KEYS
# =============================================================================

# Google AI API Key (Optional - for Gemini models)
# Get from: https://makersuite.google.com/app/apikey
# Cost: Generous free tier, then pay-per-use
GOOGLE_API_KEY=your-google-api-key-here

# Tavily API Key (Optional - for web search capabilities)
# Get from: https://tavily.com/
# Cost: Free tier available
TAVILY_API_KEY=your-tavily-api-key-here

# E2B API Key (Optional - for secure code execution)
# Get from: https://e2b.dev/
# Cost: Free tier available
E2B_API_KEY=your-e2b-api-key-here

# =============================================================================
# 🎙️ LIVEKIT CONFIGURATION (Optional)
# =============================================================================

# LiveKit API Keys (Optional - for real-time voice/video)
# Get from: https://cloud.livekit.io/
# Cost: Free tier available, then usage-based
LIVEKIT_API_KEY=your-livekit-api-key-here
LIVEKIT_API_SECRET=your-livekit-api-secret-here
LIVEKIT_URL=wss://your-livekit-server.livekit.cloud

# =============================================================================
# 🗄️ DATABASE CONFIGURATION
# =============================================================================

# Database URL (SQLite by default, PostgreSQL for production)
DATABASE_URL=sqlite:///./ai_platform.db

# PostgreSQL Configuration (Optional - for production)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=ai_platform
POSTGRES_USER=ai_platform_user
POSTGRES_PASSWORD=your-postgres-password

# =============================================================================
# 🔄 REDIS CONFIGURATION
# =============================================================================

# Redis URL (Memory-based by default, Redis server for production)
REDIS_URL=redis://localhost:6379/0

# Redis Configuration (Optional - for production)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# =============================================================================
# 🌍 ENVIRONMENT SETTINGS
# =============================================================================

# Environment (development, staging, production)
ENVIRONMENT=development

# Debug mode (true/false)
DEBUG=true

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# CORS settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:3000", "http://127.0.0.1:8000"]

# =============================================================================
# 🔧 SERVICE CONFIGURATION
# =============================================================================

# Service URLs (usually don't need to change these)
SUNA_SERVICE_URL=http://localhost:8001
AGENT_S_SERVICE_URL=http://localhost:8002
LIVEKIT_SERVICE_URL=http://localhost:8003
CREWAI_SERVICE_URL=http://localhost:8004
COPILOTKIT_SERVICE_URL=http://localhost:3000

# Service timeouts (seconds)
SERVICE_TIMEOUT=30
SERVICE_RETRY_ATTEMPTS=3
SERVICE_RETRY_DELAY=1

# =============================================================================
# 📊 MONITORING & METRICS
# =============================================================================

# Sentry DSN (Optional - for error tracking)
SENTRY_DSN=your-sentry-dsn-here

# Prometheus metrics (true/false)
ENABLE_METRICS=true

# Health check interval (seconds)
HEALTH_CHECK_INTERVAL=60

# =============================================================================
# 🔐 SUPABASE CONFIGURATION (Optional)
# =============================================================================

# Supabase (Optional - if you want to use Supabase instead of local DB)
SUPABASE_URL=your-supabase-url-here
SUPABASE_ANON_KEY=your-supabase-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# =============================================================================
# 🎯 FEATURE FLAGS
# =============================================================================

# Enable/disable specific features
ENABLE_WEB_AUTOMATION=true
ENABLE_DESKTOP_AUTOMATION=true
ENABLE_VOICE_AI=true
ENABLE_MULTI_AGENT=true
ENABLE_CODE_ASSISTANCE=true

# Rate limiting
ENABLE_RATE_LIMITING=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# =============================================================================
# 🚀 DEPLOYMENT SETTINGS
# =============================================================================

# Server configuration
HOST=0.0.0.0
PORT=8000
WORKERS=1

# SSL/TLS (for production)
SSL_CERT_PATH=
SSL_KEY_PATH=

# =============================================================================
# 📝 NOTES
# =============================================================================
# 
# QUICK START (Minimum Required):
# 1. Set OPENAI_API_KEY (get from https://platform.openai.com/api-keys)
# 2. Set ANTHROPIC_API_KEY (get from https://console.anthropic.com/)
# 3. Change SECRET_KEY and JWT_SECRET_KEY to random 32+ character strings
# 4. Change ADMIN_PASSWORD to something secure
#
# COST ESTIMATES:
# - Light usage: $5-20/month
# - Medium usage: $20-100/month  
# - Heavy usage: $100-500+/month
#
# SECURITY:
# - Never commit this file to version control
# - Use strong, unique passwords
# - Rotate API keys regularly
# - Set spending limits on API accounts
#
# =============================================================================
