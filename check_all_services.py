#!/usr/bin/env python3
"""
Quick service connectivity checker for AI Platform Integration Hub
"""

import requests
import json
from datetime import datetime

def check_service(name, url, endpoint="/health"):
    """Check if a service is running and healthy."""
    try:
        response = requests.get(f"{url}{endpoint}", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return {
                "name": name,
                "status": "✅ HEALTHY",
                "url": url,
                "response": data.get("status", "unknown"),
                "service": data.get("service", "unknown")
            }
        else:
            return {
                "name": name,
                "status": f"❌ ERROR {response.status_code}",
                "url": url,
                "response": response.text[:100],
                "service": "unknown"
            }
    except requests.exceptions.ConnectionError:
        return {
            "name": name,
            "status": "❌ CONNECTION REFUSED",
            "url": url,
            "response": "Service not running",
            "service": "unknown"
        }
    except Exception as e:
        return {
            "name": name,
            "status": f"❌ ERROR",
            "url": url,
            "response": str(e)[:100],
            "service": "unknown"
        }

def main():
    print("🔍 AI Platform Integration Hub - Service Status Check")
    print("=" * 60)
    print(f"⏰ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    services = [
        ("Integration Hub", "http://localhost:8000", "/docs"),  # Use /docs since /health needs auth
        ("Suna (Web Automation)", "http://localhost:8001", "/health"),
        ("Agent-S (Desktop)", "http://localhost:8002", "/health"),
        ("LiveKit (Voice/Video)", "http://localhost:8003", "/health"),
        ("CrewAI (Multi-Agent)", "http://localhost:8004", "/health"),
        ("CopilotKit (AI Assistant)", "http://localhost:3000", "/health"),
    ]

    results = []
    healthy_count = 0

    for name, url, endpoint in services:
        result = check_service(name, url, endpoint)
        results.append(result)
        
        if "✅" in result["status"]:
            healthy_count += 1
        
        print(f"{result['status']} {result['name']}")
        print(f"   🌐 URL: {result['url']}")
        print(f"   📊 Response: {result['response']}")
        if result.get('service') != 'unknown':
            print(f"   🔧 Service: {result['service']}")
        print()

    print("=" * 60)
    print(f"📊 SUMMARY: {healthy_count}/{len(services)} services healthy")
    
    if healthy_count == len(services):
        print("🎉 ALL SERVICES ARE RUNNING AND HEALTHY!")
        print("✅ Your AI Platform Integration Hub is fully operational!")
    elif healthy_count >= 4:
        print("⚠️  Most services are running - check the failed ones above")
    else:
        print("❌ Multiple services are down - check the logs")

    print()
    print("🔗 Quick Access Links:")
    print("   📊 Integration Hub: http://localhost:8000")
    print("   📚 API Documentation: http://localhost:8000/docs")
    print("   🌐 Suna: http://localhost:8001/docs")
    print("   🖥️  Agent-S: http://localhost:8002/docs")
    print("   🎙️  LiveKit: http://localhost:8003/docs")
    print("   👥 CrewAI: http://localhost:8004/docs")
    print("   💻 CopilotKit: http://localhost:3000/docs")

if __name__ == "__main__":
    main()
