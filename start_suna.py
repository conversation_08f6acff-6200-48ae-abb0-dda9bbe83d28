#!/usr/bin/env python3
"""
🌐 Suna Web Automation Service - Independent Startup
Starts only the Suna web automation and scraping service.
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from integration_hub.config import Config
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

def create_suna_app() -> FastAPI:
    """Create FastAPI app for Suna service only."""
    app = FastAPI(
        title="🌐 Suna Web Automation Service",
        description="Independent web automation and scraping service",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    return app

async def main():
    """Main function to start Suna service."""
    print("🌐 Starting Suna Web Automation Service...")

    # Load configuration
    config = Config()

    # Create FastAPI app
    app = create_suna_app()

    # Add Suna routes
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": "Suna Web Automation",
            "status": "running",
            "version": "1.0.0",
            "docs": "/docs",
            "capabilities": ["web_automation", "scraping", "browser_control"]
        }

    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "service": "suna",
            "port": 8001,
            "capabilities": ["web_automation", "scraping", "browser_control"]
        }

    @app.post("/api/web-automation")
    async def create_web_automation_task(task_data: dict):
        """Create a web automation task."""
        # Placeholder implementation
        return {
            "task_id": "suna_task_001",
            "status": "created",
            "type": "web_automation",
            "data": task_data,
            "message": "Suna web automation task created successfully"
        }

    @app.get("/api/tasks")
    async def list_tasks():
        """List all tasks."""
        # Placeholder implementation
        return {
            "tasks": [
                {
                    "task_id": "suna_task_001",
                    "status": "completed",
                    "type": "web_automation",
                    "created_at": "2024-01-01T00:00:00Z"
                }
            ]
        }

    @app.get("/api/capabilities")
    async def get_capabilities():
        """Get service capabilities."""
        return {
            "capabilities": [
                "web_automation",
                "scraping",
                "browser_control",
                "form_filling",
                "data_extraction"
            ]
        }
    
    # Start the server
    port = 8001  # Dedicated port for Suna
    print(f"🚀 Starting Suna service on http://localhost:{port}")
    print(f"📚 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    
    config_dict = {
        "app": app,
        "host": "0.0.0.0",
        "port": port,
        "reload": False,
        "log_level": "info"
    }
    
    await uvicorn.Server(uvicorn.Config(**config_dict)).serve()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Suna service stopped by user")
    except Exception as e:
        print(f"❌ Failed to start Suna service: {e}")
        sys.exit(1)
