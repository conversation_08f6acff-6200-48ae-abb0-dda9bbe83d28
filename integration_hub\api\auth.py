#!/usr/bin/env python3
"""
Authentication and Authorization for AI Platform Integration Hub

Provides:
- User registration and login
- JWT token management
- Role-based access control
- API key management
- Session management
- Password reset functionality
"""

import asyncio
import hashlib
import secrets
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, HTTPException, Depends, status, Request
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr, Field
import jwt
from passlib.context import CryptContext

from ..config import get_config
from .middleware import create_access_token, verify_password, get_password_hash

config = get_config()
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Forward declaration for dependency injection
def get_current_user():
    """Placeholder - actual implementation is defined later in the file."""
    pass

# In-memory storage (replace with database in production)
users_db = {}
api_keys_db = {}
sessions_db = {}
refresh_tokens_db = {}

# Request/Response Models
class UserRegistration(BaseModel):
    """User registration request."""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=8)
    full_name: Optional[str] = Field(None, max_length=100)
    role: str = Field(default="user")
    permissions: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class UserLogin(BaseModel):
    """User login request."""
    username: str
    password: str
    remember_me: bool = Field(default=False)

class TokenResponse(BaseModel):
    """Token response."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user_id: str
    role: str
    permissions: List[str]

class UserProfile(BaseModel):
    """User profile response."""
    user_id: str
    username: str
    email: str
    full_name: Optional[str]
    role: str
    permissions: List[str]
    created_at: datetime
    last_login: Optional[datetime]
    is_active: bool
    metadata: Dict[str, Any]

class PasswordReset(BaseModel):
    """Password reset request."""
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    """Password reset confirmation."""
    token: str
    new_password: str = Field(..., min_length=8)

class APIKeyCreate(BaseModel):
    """API key creation request."""
    name: str = Field(..., max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    permissions: List[str] = Field(default_factory=list)
    expires_at: Optional[datetime] = None

class APIKeyResponse(BaseModel):
    """API key response."""
    key_id: str
    name: str
    description: Optional[str]
    api_key: str
    permissions: List[str]
    created_at: datetime
    expires_at: Optional[datetime]
    last_used: Optional[datetime]
    is_active: bool

class User:
    """User model."""
    
    def __init__(
        self,
        user_id: str,
        username: str,
        email: str,
        password_hash: str,
        full_name: Optional[str] = None,
        role: str = "user",
        permissions: List[str] = None,
        metadata: Dict[str, Any] = None
    ):
        self.user_id = user_id
        self.username = username
        self.email = email
        self.password_hash = password_hash
        self.full_name = full_name
        self.role = role
        self.permissions = permissions or []
        self.metadata = metadata or {}
        self.created_at = datetime.utcnow()
        self.last_login = None
        self.is_active = True
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary."""
        return {
            "user_id": self.user_id,
            "username": self.username,
            "email": self.email,
            "full_name": self.full_name,
            "role": self.role,
            "permissions": self.permissions,
            "created_at": self.created_at.isoformat(),
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "is_active": self.is_active,
            "metadata": self.metadata
        }
    
    def check_password(self, password: str) -> bool:
        """Check if password is correct."""
        return verify_password(password, self.password_hash)
    
    def update_last_login(self):
        """Update last login timestamp."""
        self.last_login = datetime.utcnow()
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def increment_failed_login(self):
        """Increment failed login attempts."""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:
            self.locked_until = datetime.utcnow() + timedelta(minutes=30)
    
    def is_locked(self) -> bool:
        """Check if account is locked."""
        if self.locked_until and datetime.utcnow() < self.locked_until:
            return True
        return False

class APIKey:
    """API key model."""
    
    def __init__(
        self,
        key_id: str,
        user_id: str,
        name: str,
        api_key: str,
        description: Optional[str] = None,
        permissions: List[str] = None,
        expires_at: Optional[datetime] = None
    ):
        self.key_id = key_id
        self.user_id = user_id
        self.name = name
        self.api_key = api_key
        self.description = description
        self.permissions = permissions or []
        self.created_at = datetime.utcnow()
        self.expires_at = expires_at
        self.last_used = None
        self.is_active = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert API key to dictionary."""
        return {
            "key_id": self.key_id,
            "user_id": self.user_id,
            "name": self.name,
            "description": self.description,
            "permissions": self.permissions,
            "created_at": self.created_at.isoformat(),
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "is_active": self.is_active
        }
    
    def is_expired(self) -> bool:
        """Check if API key is expired."""
        if self.expires_at and datetime.utcnow() > self.expires_at:
            return True
        return False
    
    def update_last_used(self):
        """Update last used timestamp."""
        self.last_used = datetime.utcnow()

# Authentication router
auth_router = APIRouter(prefix="/auth", tags=["Authentication"])

@auth_router.post("/register", response_model=UserProfile)
async def register_user(user_data: UserRegistration):
    """Register a new user."""
    try:
        # Check if username or email already exists
        for user in users_db.values():
            if user.username == user_data.username:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already registered"
                )
            if user.email == user_data.email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
        
        # Create new user
        user_id = str(uuid.uuid4())
        password_hash = get_password_hash(user_data.password)
        
        user = User(
            user_id=user_id,
            username=user_data.username,
            email=user_data.email,
            password_hash=password_hash,
            full_name=user_data.full_name,
            role=user_data.role,
            permissions=user_data.permissions,
            metadata=user_data.metadata
        )
        
        users_db[user_id] = user
        
        return UserProfile(
            user_id=user.user_id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            role=user.role,
            permissions=user.permissions,
            created_at=user.created_at,
            last_login=user.last_login,
            is_active=user.is_active,
            metadata=user.metadata
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

@auth_router.post("/login", response_model=TokenResponse)
async def login_user(login_data: UserLogin):
    """Authenticate user and return tokens."""
    try:
        # Find user by username
        user = None
        for u in users_db.values():
            if u.username == login_data.username:
                user = u
                break
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password"
            )
        
        # Check if account is locked
        if user.is_locked():
            raise HTTPException(
                status_code=status.HTTP_423_LOCKED,
                detail="Account is temporarily locked due to failed login attempts"
            )
        
        # Check if account is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is disabled"
            )
        
        # Verify password
        if not user.check_password(login_data.password):
            user.increment_failed_login()
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password"
            )
        
        # Update login info
        user.update_last_login()
        
        # Create tokens
        access_token_expires = timedelta(hours=config.security.jwt_expiration_hours)
        refresh_token_expires = timedelta(days=30 if login_data.remember_me else 7)
        
        access_token_data = {
            "user_id": user.user_id,
            "username": user.username,
            "role": user.role,
            "permissions": user.permissions,
            "token_type": "access"
        }
        
        refresh_token_data = {
            "user_id": user.user_id,
            "token_type": "refresh"
        }
        
        access_token = create_access_token(access_token_data, access_token_expires)
        refresh_token = create_access_token(refresh_token_data, refresh_token_expires)
        
        # Store refresh token
        refresh_tokens_db[refresh_token] = {
            "user_id": user.user_id,
            "created_at": datetime.utcnow(),
            "expires_at": datetime.utcnow() + refresh_token_expires
        }
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=int(access_token_expires.total_seconds()),
            user_id=user.user_id,
            role=user.role,
            permissions=user.permissions
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )

@auth_router.post("/refresh", response_model=TokenResponse)
async def refresh_token(refresh_token: str):
    """Refresh access token using refresh token."""
    try:
        # Verify refresh token
        try:
            payload = jwt.decode(
                refresh_token,
                config.security.jwt_secret_key,
                algorithms=[config.security.jwt_algorithm]
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        # Check if refresh token exists and is valid
        if refresh_token not in refresh_tokens_db:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Refresh token not found"
            )
        
        token_info = refresh_tokens_db[refresh_token]
        if datetime.utcnow() > token_info["expires_at"]:
            del refresh_tokens_db[refresh_token]
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Refresh token expired"
            )
        
        # Get user
        user_id = payload.get("user_id")
        user = users_db.get(user_id)
        
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        # Create new access token
        access_token_expires = timedelta(hours=config.security.jwt_expiration_hours)
        access_token_data = {
            "user_id": user.user_id,
            "username": user.username,
            "role": user.role,
            "permissions": user.permissions,
            "token_type": "access"
        }
        
        access_token = create_access_token(access_token_data, access_token_expires)
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=int(access_token_expires.total_seconds()),
            user_id=user.user_id,
            role=user.role,
            permissions=user.permissions
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Token refresh failed: {str(e)}"
        )

@auth_router.post("/logout")
async def logout_user(refresh_token: str):
    """Logout user and invalidate refresh token."""
    try:
        # Remove refresh token
        if refresh_token in refresh_tokens_db:
            del refresh_tokens_db[refresh_token]
        
        return {"message": "Logged out successfully"}
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Logout failed: {str(e)}"
        )

@auth_router.get("/profile", response_model=UserProfile)
async def get_user_profile(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Get current user profile."""
    try:
        user = users_db.get(current_user["user_id"])
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return UserProfile(
            user_id=user.user_id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            role=user.role,
            permissions=user.permissions,
            created_at=user.created_at,
            last_login=user.last_login,
            is_active=user.is_active,
            metadata=user.metadata
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get profile: {str(e)}"
        )

@auth_router.post("/api-keys", response_model=APIKeyResponse)
async def create_api_key(
    key_data: APIKeyCreate,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new API key."""
    try:
        # Generate API key
        key_id = str(uuid.uuid4())
        api_key = f"ak_{secrets.token_urlsafe(32)}"
        
        # Create API key object
        api_key_obj = APIKey(
            key_id=key_id,
            user_id=current_user["user_id"],
            name=key_data.name,
            api_key=api_key,
            description=key_data.description,
            permissions=key_data.permissions,
            expires_at=key_data.expires_at
        )
        
        api_keys_db[api_key] = api_key_obj
        
        return APIKeyResponse(
            key_id=api_key_obj.key_id,
            name=api_key_obj.name,
            description=api_key_obj.description,
            api_key=api_key_obj.api_key,
            permissions=api_key_obj.permissions,
            created_at=api_key_obj.created_at,
            expires_at=api_key_obj.expires_at,
            last_used=api_key_obj.last_used,
            is_active=api_key_obj.is_active
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create API key: {str(e)}"
        )

@auth_router.get("/api-keys", response_model=List[APIKeyResponse])
async def list_api_keys(current_user: Dict[str, Any] = Depends(get_current_user)):
    """List user's API keys."""
    try:
        user_keys = []
        for api_key_obj in api_keys_db.values():
            if api_key_obj.user_id == current_user["user_id"]:
                # Don't return the actual API key in list
                user_keys.append(APIKeyResponse(
                    key_id=api_key_obj.key_id,
                    name=api_key_obj.name,
                    description=api_key_obj.description,
                    api_key="ak_****",  # Masked
                    permissions=api_key_obj.permissions,
                    created_at=api_key_obj.created_at,
                    expires_at=api_key_obj.expires_at,
                    last_used=api_key_obj.last_used,
                    is_active=api_key_obj.is_active
                ))
        
        return user_keys
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list API keys: {str(e)}"
        )

@auth_router.delete("/api-keys/{key_id}")
async def delete_api_key(
    key_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Delete an API key."""
    try:
        # Find and delete API key
        for api_key, api_key_obj in list(api_keys_db.items()):
            if api_key_obj.key_id == key_id and api_key_obj.user_id == current_user["user_id"]:
                del api_keys_db[api_key]
                return {"message": "API key deleted successfully"}
        
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="API key not found"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete API key: {str(e)}"
        )

# Utility functions (moved to top to avoid forward reference issues)
async def get_current_user_impl(request: Request) -> Dict[str, Any]:
    """Get current user from request (JWT or API key)."""
    # Try JWT token first
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header.split(" ")[1]
        try:
            return await get_current_user_from_token(
                HTTPAuthorizationCredentials(scheme="Bearer", credentials=token)
            )
        except HTTPException:
            pass

    # Try API key
    api_key = request.headers.get("X-API-Key")
    if api_key:
        return await get_current_user_from_api_key(api_key)

    # No valid authentication found
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Authentication required"
    )

# Replace the placeholder with the actual implementation
get_current_user = get_current_user_impl

def setup_auth(app):
    """Setup authentication for the FastAPI app."""
    app.include_router(auth_router, prefix="/auth", tags=["authentication"])
    return app

async def get_current_user_from_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Get current user from JWT token."""
    try:
        payload = jwt.decode(
            credentials.credentials,
            config.security.jwt_secret_key,
            algorithms=[config.security.jwt_algorithm]
        )
        
        user_id = payload.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        
        user = users_db.get(user_id)
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        return {
            "user_id": user.user_id,
            "username": user.username,
            "role": user.role,
            "permissions": user.permissions
        }
    
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

async def get_current_user_from_api_key(api_key: str) -> Dict[str, Any]:
    """Get current user from API key."""
    api_key_obj = api_keys_db.get(api_key)
    
    if not api_key_obj or not api_key_obj.is_active or api_key_obj.is_expired():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired API key"
        )
    
    user = users_db.get(api_key_obj.user_id)
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Update last used
    api_key_obj.update_last_used()
    
    return {
        "user_id": user.user_id,
        "username": user.username,
        "role": user.role,
        "permissions": api_key_obj.permissions or user.permissions
    }



# Initialize default admin user
async def initialize_default_users():
    """Initialize default users for the system."""
    try:
        # Create default admin user if no users exist
        if not users_db:
            admin_user = User(
                user_id="admin",
                username="admin",
                email="<EMAIL>",
                password_hash=get_password_hash("admin123"),
                full_name="System Administrator",
                role="admin",
                permissions=["*"],  # All permissions
                metadata={"created_by": "system"}
            )
            
            users_db["admin"] = admin_user
            
            print("🔐 Default admin user created:")
            print("   Username: admin")
            print("   Password: admin123")
            print("   ⚠️  Please change the default password!")
    
    except Exception as e:
        print(f"❌ Failed to initialize default users: {str(e)}")