#!/usr/bin/env python3
"""
Main FastAPI Application for AI Platform Integration Hub

This is the main entry point that:
- Initializes the FastAPI application
- Sets up middleware and authentication
- Registers API routes
- Configures the IntegrationHub
- Starts all AI services
- Provides health monitoring and metrics
"""

import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.gzip import GZipMiddleware
import uvicorn

from .config import get_config
from .main import IntegrationHub
from .api.routes import router as api_router
from .api.auth import auth_router, initialize_default_users
from .api.middleware import setup_middleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('ai_platform.log')
    ]
)
logger = logging.getLogger(__name__)

# Global variables
integration_hub: IntegrationHub = None
config = get_config()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global integration_hub
    
    # Startup
    logger.info("🚀 Starting AI Platform Integration Hub...")
    
    try:
        # Initialize default users
        await initialize_default_users()
        
        # Initialize IntegrationHub
        integration_hub = IntegrationHub()
        await integration_hub.initialize()
        
        # Make integration_hub available globally
        app.state.integration_hub = integration_hub
        
        logger.info("✅ AI Platform Integration Hub started successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ Failed to start AI Platform: {str(e)}")
        raise
    
    finally:
        # Shutdown
        logger.info("🛑 Shutting down AI Platform Integration Hub...")
        
        if integration_hub:
            await integration_hub.shutdown()
        
        logger.info("✅ AI Platform Integration Hub shut down successfully")

# Create FastAPI application
app = FastAPI(
    title="AI Platform Integration Hub",
    description="""
    Unified AI Platform integrating CopilotKit, Suna, Agent-S, LiveKit Agents, and CrewAI.
    
    This platform provides:
    - Web automation (Suna)
    - Desktop automation (Agent-S)
    - Real-time voice/video AI (LiveKit Agents)
    - Multi-agent workflows (CrewAI)
    - Frontend AI assistance (CopilotKit)
    
    All services are orchestrated through a unified API with task management,
    health monitoring, and comprehensive analytics.
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Add compression middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Setup all middleware (CORS, auth, rate limiting, etc.)
setup_middleware(app)

# Include routers
app.include_router(auth_router)
app.include_router(api_router)

# Root endpoint is now handled by the dashboard route in api/routes.py

# Redirect /api to docs
@app.get("/api")
async def api_redirect():
    """Redirect /api to documentation."""
    return RedirectResponse(url="/docs")

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors."""
    logger.error(
        f"❌ Unhandled exception in {request.method} {request.url.path}: {str(exc)}",
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "path": str(request.url.path),
            "method": request.method
        }
    )

# HTTP exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions with proper logging."""
    if exc.status_code >= 500:
        logger.error(
            f"❌ HTTP {exc.status_code} in {request.method} {request.url.path}: {exc.detail}"
        )
    elif exc.status_code >= 400:
        logger.warning(
            f"⚠️ HTTP {exc.status_code} in {request.method} {request.url.path}: {exc.detail}"
        )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.detail,
            "status_code": exc.status_code,
            "path": str(request.url.path),
            "method": request.method
        }
    )

# Health check endpoint (no auth required)
@app.get("/health")
async def health_check():
    """Simple health check endpoint."""
    try:
        if integration_hub:
            health_status = await integration_hub.get_health_status()
            return {
                "status": "healthy" if health_status.get("overall_status") == "healthy" else "unhealthy",
                "timestamp": health_status.get("timestamp"),
                "services": len(health_status.get("services", {})),
                "uptime": health_status.get("uptime")
            }
        else:
            return {
                "status": "starting",
                "message": "Integration hub is initializing"
            }
    except Exception as e:
        logger.error(f"❌ Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

# Status endpoint with detailed information
@app.get("/status")
async def status_check():
    """Detailed status endpoint."""
    try:
        if not integration_hub:
            return {
                "status": "initializing",
                "message": "Integration hub is starting up"
            }
        
        # Get comprehensive status
        health_status = await integration_hub.get_health_status()
        metrics = await integration_hub.get_system_metrics()
        
        return {
            "platform": {
                "name": "AI Platform Integration Hub",
                "version": "1.0.0",
                "status": health_status.get("overall_status", "unknown"),
                "uptime": health_status.get("uptime", 0)
            },
            "services": health_status.get("services", {}),
            "metrics": {
                "total_tasks": metrics.get("total_tasks", 0),
                "active_tasks": metrics.get("active_tasks", 0),
                "completed_tasks": metrics.get("completed_tasks", 0),
                "failed_tasks": metrics.get("failed_tasks", 0),
                "average_response_time": metrics.get("average_response_time", 0)
            },
            "resources": {
                "memory_usage": metrics.get("memory_usage", 0),
                "cpu_usage": metrics.get("cpu_usage", 0),
                "disk_usage": metrics.get("disk_usage", 0)
            },
            "timestamp": health_status.get("timestamp")
        }
    
    except Exception as e:
        logger.error(f"❌ Status check failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e)
        }

# Dependency to get IntegrationHub instance
async def get_integration_hub() -> IntegrationHub:
    """Dependency to get the IntegrationHub instance."""
    if not integration_hub:
        raise HTTPException(
            status_code=503,
            detail="Integration hub is not available"
        )
    return integration_hub

# Make the dependency available in the main module
app.dependency_overrides[get_integration_hub] = get_integration_hub

# Signal handlers for graceful shutdown
def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"📡 Received signal {signum}, initiating graceful shutdown...")
    
    # Create a task to shutdown the integration hub
    if integration_hub:
        asyncio.create_task(integration_hub.shutdown())
    
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Development server function
def run_development_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    reload: bool = True,
    log_level: str = "info"
):
    """Run the development server."""
    logger.info(f"🚀 Starting development server on {host}:{port}")
    
    uvicorn.run(
        "integration_hub.app:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level,
        access_log=True,
        use_colors=True
    )

# Production server function
def run_production_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    workers: int = 1,
    log_level: str = "info"
):
    """Run the production server."""
    logger.info(f"🚀 Starting production server on {host}:{port} with {workers} workers")
    
    uvicorn.run(
        "integration_hub.app:app",
        host=host,
        port=port,
        workers=workers,
        log_level=log_level,
        access_log=True,
        use_colors=False,
        loop="uvloop",  # Use uvloop for better performance
        http="httptools"  # Use httptools for better performance
    )

# CLI entry point
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="AI Platform Integration Hub")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload (development)")
    parser.add_argument("--production", action="store_true", help="Run in production mode")
    parser.add_argument("--log-level", default="info", help="Log level")
    
    args = parser.parse_args()
    
    if args.production:
        run_production_server(
            host=args.host,
            port=args.port,
            workers=args.workers,
            log_level=args.log_level
        )
    else:
        run_development_server(
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level
        )