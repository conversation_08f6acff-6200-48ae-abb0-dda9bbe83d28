#!/usr/bin/env python3
"""
🚀 AI Platform Services Controller
Start individual AI services or all services together.
"""

import os
import sys
import subprocess
import time
import argparse
from pathlib import Path

# Service definitions
SERVICES = {
    "dashboard": {
        "script": "start_dashboard.py",
        "name": "📊 AI Platform Dashboard",
        "port": 8000,
        "description": "Web dashboard for monitoring and controlling AI services"
    },
    "suna": {
        "script": "start_suna.py",
        "name": "🌐 Suna Web Automation",
        "port": 8001,
        "description": "Web automation and scraping service"
    },
    "agent-s": {
        "script": "start_agent_s.py",
        "name": "🖥️ Agent-S Desktop Automation",
        "port": 8002,
        "description": "Desktop automation service"
    },
    "livekit": {
        "script": "start_livekit.py",
        "name": "🎙️ LiveKit Voice/Video AI",
        "port": 8003,
        "description": "Real-time voice and video AI service"
    },
    "crewai": {
        "script": "start_crewai.py",
        "name": "👥 CrewAI Multi-Agent",
        "port": 8004,
        "description": "Multi-agent workflow service"
    },
    "copilotkit": {
        "script": "start_copilotkit.py",
        "name": "🤖 CopilotKit Frontend AI",
        "port": 3000,
        "description": "Frontend AI assistance service"
    }
}

def print_banner():
    """Print the application banner."""
    print("=" * 60)
    print("🤖 AI PLATFORM INTEGRATION HUB")
    print("Independent Service Controller")
    print("=" * 60)

def list_services():
    """List all available services."""
    print("\n📋 Available Services:")
    print("-" * 60)
    for service_id, service_info in SERVICES.items():
        print(f"  {service_info['name']}")
        print(f"    ID: {service_id}")
        print(f"    Port: {service_info['port']}")
        print(f"    Description: {service_info['description']}")
        print()

def start_service(service_id: str):
    """Start a specific service."""
    if service_id not in SERVICES:
        print(f"❌ Unknown service: {service_id}")
        print("Available services:", ", ".join(SERVICES.keys()))
        return False
    
    service = SERVICES[service_id]
    script_path = Path(__file__).parent / service["script"]
    
    if not script_path.exists():
        print(f"❌ Service script not found: {script_path}")
        return False
    
    print(f"🚀 Starting {service['name']}...")
    print(f"   Script: {service['script']}")
    print(f"   Port: {service['port']}")
    print(f"   URL: http://localhost:{service['port']}")
    print()
    
    try:
        # Start the service
        subprocess.run([sys.executable, str(script_path)], check=True)
        return True
    except KeyboardInterrupt:
        print(f"\n🛑 {service['name']} stopped by user")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start {service['name']}: {e}")
        return False
    except Exception as e:
        print(f"❌ Error starting {service['name']}: {e}")
        return False

def start_all_services():
    """Start all services (not recommended for development)."""
    print("⚠️  Starting all services simultaneously...")
    print("⚠️  This is not recommended for development/debugging.")
    print("⚠️  Consider starting services individually instead.")
    print()
    
    response = input("Continue? (y/N): ").strip().lower()
    if response != 'y':
        print("Cancelled.")
        return
    
    processes = []
    
    try:
        for service_id, service in SERVICES.items():
            script_path = Path(__file__).parent / service["script"]
            if script_path.exists():
                print(f"🚀 Starting {service['name']}...")
                process = subprocess.Popen([sys.executable, str(script_path)])
                processes.append((service_id, process))
                time.sleep(2)  # Stagger startup
        
        print(f"\n✅ Started {len(processes)} services")
        print("Press Ctrl+C to stop all services")
        
        # Wait for all processes
        for service_id, process in processes:
            process.wait()
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping all services...")
        for service_id, process in processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            print(f"✅ Stopped {SERVICES[service_id]['name']}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="AI Platform Services Controller",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python start_services.py --list                    # List all services
  python start_services.py --service dashboard       # Start dashboard only
  python start_services.py --service suna           # Start Suna service only
  python start_services.py --all                    # Start all services (not recommended)
        """
    )
    
    parser.add_argument("--list", action="store_true", help="List all available services")
    parser.add_argument("--service", type=str, help="Start a specific service")
    parser.add_argument("--all", action="store_true", help="Start all services (not recommended)")
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.list:
        list_services()
    elif args.service:
        start_service(args.service)
    elif args.all:
        start_all_services()
    else:
        print("🎯 Quick Start Guide:")
        print()
        print("1. Start the dashboard first:")
        print("   python start_services.py --service dashboard")
        print()
        print("2. Then start individual services as needed:")
        print("   python start_services.py --service suna")
        print("   python start_services.py --service agent-s")
        print("   python start_services.py --service livekit")
        print("   python start_services.py --service crewai")
        print("   python start_services.py --service copilotkit")
        print()
        print("3. View all available services:")
        print("   python start_services.py --list")
        print()
        print("💡 Tip: Start services in separate terminal windows for easier debugging")

if __name__ == "__main__":
    main()
