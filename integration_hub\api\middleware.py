#!/usr/bin/env python3
"""
Middleware for AI Platform Integration Hub API

Provides:
- Authentication and authorization
- CORS handling
- Rate limiting
- Request/response logging
- Error handling
- Security headers
- Request validation
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from collections import defaultdict

from fastapi import Request, Response, HTTPException, status, Depends
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
import jwt
from passlib.context import CryptContext

from ..config import get_config

logger = logging.getLogger(__name__)
config = get_config()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Rate limiting storage (in production, use Redis)
rate_limit_storage = defaultdict(list)

class AuthenticationMiddleware(BaseHTTPMiddleware):
    """Authentication and authorization middleware."""
    
    def __init__(self, app, secret_key: str, algorithm: str = "HS256"):
        super().__init__(app)
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.exempt_paths = {
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/health/",
            "/api/admin/config"
        }
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Process authentication for incoming requests."""
        # Skip authentication for exempt paths
        if any(request.url.path.startswith(path) for path in self.exempt_paths):
            return await call_next(request)
        
        # Extract token from Authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Missing or invalid authorization header"}
            )
        
        token = auth_header.split(" ")[1]
        
        try:
            # Decode and validate JWT token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token expiration
            exp = payload.get("exp")
            if exp and datetime.utcnow().timestamp() > exp:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Token has expired"}
                )
            
            # Add user info to request state
            request.state.user_id = payload.get("user_id")
            request.state.user_role = payload.get("role", "user")
            request.state.permissions = payload.get("permissions", [])
            
        except jwt.InvalidTokenError as e:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": f"Invalid token: {str(e)}"}
            )
        except Exception as e:
            logger.error(f"❌ Authentication error: {str(e)}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "Authentication service error"}
            )
        
        return await call_next(request)

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware."""
    
    def __init__(self, app, requests_per_minute: int = 60, requests_per_hour: int = 1000):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.exempt_paths = {
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/health/"
        }
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Apply rate limiting to incoming requests."""
        # Skip rate limiting for exempt paths
        if any(request.url.path.startswith(path) for path in self.exempt_paths):
            return await call_next(request)
        
        # Get client identifier (IP address or user ID)
        client_id = self._get_client_id(request)
        current_time = datetime.utcnow()
        
        # Clean old entries
        self._cleanup_old_entries(client_id, current_time)
        
        # Check rate limits
        if self._is_rate_limited(client_id, current_time):
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "detail": "Rate limit exceeded",
                    "retry_after": 60
                },
                headers={"Retry-After": "60"}
            )
        
        # Record request
        rate_limit_storage[client_id].append(current_time)
        
        response = await call_next(request)
        
        # Add rate limit headers
        remaining_requests = self._get_remaining_requests(client_id, current_time)
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining_requests)
        response.headers["X-RateLimit-Reset"] = str(int((current_time + timedelta(minutes=1)).timestamp()))
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting."""
        # Use user ID if authenticated, otherwise use IP address
        if hasattr(request.state, 'user_id') and request.state.user_id:
            return f"user_{request.state.user_id}"
        
        # Get IP address from headers or client
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return f"ip_{forwarded_for.split(',')[0].strip()}"
        
        return f"ip_{request.client.host if request.client else 'unknown'}"
    
    def _cleanup_old_entries(self, client_id: str, current_time: datetime):
        """Remove old entries from rate limit storage."""
        if client_id in rate_limit_storage:
            # Remove entries older than 1 hour
            cutoff_time = current_time - timedelta(hours=1)
            rate_limit_storage[client_id] = [
                timestamp for timestamp in rate_limit_storage[client_id]
                if timestamp > cutoff_time
            ]
    
    def _is_rate_limited(self, client_id: str, current_time: datetime) -> bool:
        """Check if client is rate limited."""
        if client_id not in rate_limit_storage:
            return False
        
        timestamps = rate_limit_storage[client_id]
        
        # Check requests per minute
        minute_ago = current_time - timedelta(minutes=1)
        recent_requests = sum(1 for ts in timestamps if ts > minute_ago)
        if recent_requests >= self.requests_per_minute:
            return True
        
        # Check requests per hour
        hour_ago = current_time - timedelta(hours=1)
        hourly_requests = sum(1 for ts in timestamps if ts > hour_ago)
        if hourly_requests >= self.requests_per_hour:
            return True
        
        return False
    
    def _get_remaining_requests(self, client_id: str, current_time: datetime) -> int:
        """Get remaining requests for the current minute."""
        if client_id not in rate_limit_storage:
            return self.requests_per_minute
        
        minute_ago = current_time - timedelta(minutes=1)
        recent_requests = sum(1 for ts in rate_limit_storage[client_id] if ts > minute_ago)
        return max(0, self.requests_per_minute - recent_requests)

class LoggingMiddleware(BaseHTTPMiddleware):
    """Request/response logging middleware."""
    
    def __init__(self, app, log_requests: bool = True, log_responses: bool = True):
        super().__init__(app)
        self.log_requests = log_requests
        self.log_responses = log_responses
        self.sensitive_headers = {
            "authorization",
            "x-api-key",
            "cookie",
            "set-cookie"
        }
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Log incoming requests and outgoing responses."""
        start_time = time.time()
        
        # Log request
        if self.log_requests:
            await self._log_request(request)
        
        # Process request
        try:
            response = await call_next(request)
        except Exception as e:
            # Log error
            logger.error(f"❌ Request processing error: {str(e)}")
            response = JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "Internal server error"}
            )
        
        # Calculate processing time
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        
        # Log response
        if self.log_responses:
            await self._log_response(request, response, process_time)
        
        return response
    
    async def _log_request(self, request: Request):
        """Log incoming request details."""
        # Sanitize headers
        headers = dict(request.headers)
        for header in self.sensitive_headers:
            if header in headers:
                headers[header] = "[REDACTED]"
        
        # Get client info
        client_id = self._get_client_info(request)
        
        logger.info(
            f"📥 {request.method} {request.url.path} - "
            f"Client: {client_id} - "
            f"Query: {dict(request.query_params)} - "
            f"Headers: {headers}"
        )
    
    async def _log_response(self, request: Request, response: Response, process_time: float):
        """Log outgoing response details."""
        # Get client info
        client_id = self._get_client_info(request)
        
        # Determine log level based on status code
        if response.status_code >= 500:
            log_level = logging.ERROR
            emoji = "❌"
        elif response.status_code >= 400:
            log_level = logging.WARNING
            emoji = "⚠️"
        else:
            log_level = logging.INFO
            emoji = "📤"
        
        logger.log(
            log_level,
            f"{emoji} {request.method} {request.url.path} - "
            f"Status: {response.status_code} - "
            f"Client: {client_id} - "
            f"Time: {process_time:.3f}s"
        )
    
    def _get_client_info(self, request: Request) -> str:
        """Get client information for logging."""
        # Use user ID if authenticated
        if hasattr(request.state, 'user_id') and request.state.user_id:
            return f"User:{request.state.user_id}"
        
        # Use IP address
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return f"IP:{forwarded_for.split(',')[0].strip()}"
        
        return f"IP:{request.client.host if request.client else 'unknown'}"

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Security headers middleware."""
    
    def __init__(self, app):
        super().__init__(app)
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Add security headers to responses."""
        response = await call_next(request)
        
        # Add security headers
        for header, value in self.security_headers.items():
            response.headers[header] = value
        
        return response

class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Global error handling middleware."""
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Handle uncaught exceptions."""
        try:
            return await call_next(request)
        
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        
        except Exception as e:
            # Log unexpected errors
            logger.error(
                f"❌ Unhandled exception in {request.method} {request.url.path}: {str(e)}",
                exc_info=True
            )
            
            # Return generic error response
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "detail": "Internal server error",
                    "error_id": f"error_{int(time.time())}",
                    "timestamp": datetime.utcnow().isoformat()
                }
            )

class RequestValidationMiddleware(BaseHTTPMiddleware):
    """Request validation middleware."""
    
    def __init__(self, app, max_request_size: int = 10 * 1024 * 1024):  # 10MB
        super().__init__(app)
        self.max_request_size = max_request_size
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Validate incoming requests."""
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_request_size:
            return JSONResponse(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                content={"detail": "Request entity too large"}
            )
        
        # Check content type for POST/PUT requests
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            if not content_type.startswith(("application/json", "multipart/form-data")):
                return JSONResponse(
                    status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                    content={"detail": "Unsupported media type"}
                )
        
        return await call_next(request)

# Utility functions for authentication
def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=getattr(config.security, 'jwt_expiration_hours', 24))

    to_encode.update({"exp": expire})

    return jwt.encode(
        to_encode,
        config.security.secret_key,
        algorithm=getattr(config.security, 'jwt_algorithm', 'HS256')
    )

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)

def setup_cors_middleware(app, allowed_origins: List[str] = None):
    """Setup CORS middleware."""
    if allowed_origins is None:
        allowed_origins = [
            "http://localhost:3000",
            "http://localhost:3001",
            "http://localhost:8000",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
            "http://127.0.0.1:8000"
        ]
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
        allow_headers=[
            "Authorization",
            "Content-Type",
            "X-Requested-With",
            "Accept",
            "Origin",
            "User-Agent",
            "DNT",
            "Cache-Control",
            "X-Mx-ReqToken",
            "Keep-Alive",
            "X-Requested-With",
            "If-Modified-Since"
        ],
        expose_headers=[
            "X-Process-Time",
            "X-RateLimit-Limit",
            "X-RateLimit-Remaining",
            "X-RateLimit-Reset"
        ]
    )

def setup_middleware(app):
    """Setup all middleware for the application."""
    # Add middleware in reverse order (last added = first executed)
    
    # Error handling (outermost)
    app.add_middleware(ErrorHandlingMiddleware)
    
    # Security headers
    app.add_middleware(SecurityHeadersMiddleware)
    
    # Request validation
    app.add_middleware(RequestValidationMiddleware)
    
    # Rate limiting
    app.add_middleware(
        RateLimitMiddleware,
        requests_per_minute=config.security.api_rate_limit,
        requests_per_hour=config.security.api_rate_limit * 60
    )
    
    # Authentication
    app.add_middleware(
        AuthenticationMiddleware,
        secret_key=config.security.secret_key,
        algorithm=getattr(config.security, 'jwt_algorithm', 'HS256')
    )
    
    # Logging
    app.add_middleware(
        LoggingMiddleware,
        log_requests=True,
        log_responses=True
    )
    
    # CORS (innermost, closest to the application)
    setup_cors_middleware(app)
    
    logger.info("🔧 All middleware configured successfully")

# Authentication dependencies for route protection
async def get_current_user(request: Request) -> Dict[str, Any]:
    """Get current authenticated user from request."""
    if not hasattr(request.state, 'user_id') or not request.state.user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated"
        )
    
    return {
        "user_id": request.state.user_id,
        "role": getattr(request.state, 'user_role', 'user'),
        "permissions": getattr(request.state, 'permissions', [])
    }

async def require_admin(current_user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
    """Require admin role for access."""
    if current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    return current_user

async def require_permission(permission: str):
    """Create a dependency that requires a specific permission."""
    async def check_permission(current_user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
        user_permissions = current_user.get("permissions", [])
        if permission not in user_permissions and current_user.get("role") != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return current_user
    
    return check_permission