"""
Standalone CrewAI Service Wrapper

This is a simplified version of CrewAI that provides basic multi-agent
workflow capabilities without complex dependencies.
"""

import os
import asyncio
import logging
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import uvicorn
import sys
from datetime import datetime, timezone

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Windows event loop policy
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

app = FastAPI(title="CrewAI Standalone Service", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class Agent(BaseModel):
    name: str
    role: str
    goal: str
    backstory: str
    tools: Optional[List[str]] = []

class Task(BaseModel):
    description: str
    agent: str
    expected_output: str
    dependencies: Optional[List[str]] = []

class CrewWorkflowRequest(BaseModel):
    workflow_name: str
    agents: List[Agent]
    tasks: List[Task]
    execution_mode: str = "sequential"  # sequential, parallel
    max_iterations: int = 5
    options: Optional[Dict[str, Any]] = {}

class CrewWorkflowResponse(BaseModel):
    success: bool
    workflow_id: str
    results: Optional[Dict[str, Any]] = None
    message: str
    timestamp: str

class HealthResponse(BaseModel):
    status: str
    timestamp: str
    service: str
    version: str

# Mock workflow execution functions
async def mock_execute_sequential_workflow(workflow: CrewWorkflowRequest) -> Dict[str, Any]:
    """Mock sequential workflow execution."""
    await asyncio.sleep(1.0)  # Simulate processing time
    
    results = {
        "workflow_name": workflow.workflow_name,
        "execution_mode": "sequential",
        "agents_used": [agent.name for agent in workflow.agents],
        "tasks_completed": len(workflow.tasks),
        "task_results": []
    }
    
    for i, task in enumerate(workflow.tasks):
        task_result = {
            "task_id": f"task_{i+1}",
            "description": task.description,
            "agent": task.agent,
            "status": "completed",
            "output": f"Mock output for task: {task.description}",
            "execution_time": "0.2s",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        results["task_results"].append(task_result)
        await asyncio.sleep(0.2)  # Simulate task execution time
    
    return results

async def mock_execute_parallel_workflow(workflow: CrewWorkflowRequest) -> Dict[str, Any]:
    """Mock parallel workflow execution."""
    await asyncio.sleep(0.8)  # Simulate processing time
    
    results = {
        "workflow_name": workflow.workflow_name,
        "execution_mode": "parallel",
        "agents_used": [agent.name for agent in workflow.agents],
        "tasks_completed": len(workflow.tasks),
        "task_results": []
    }
    
    # Simulate parallel execution
    for i, task in enumerate(workflow.tasks):
        task_result = {
            "task_id": f"task_{i+1}",
            "description": task.description,
            "agent": task.agent,
            "status": "completed",
            "output": f"Mock parallel output for task: {task.description}",
            "execution_time": "0.8s",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        results["task_results"].append(task_result)
    
    return results

async def mock_create_agent_team(agents: List[Agent]) -> Dict[str, Any]:
    """Mock agent team creation."""
    await asyncio.sleep(0.3)
    
    return {
        "team_id": f"team_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "agents": [
            {
                "name": agent.name,
                "role": agent.role,
                "status": "ready",
                "capabilities": agent.tools
            } for agent in agents
        ],
        "team_size": len(agents),
        "created_at": datetime.now(timezone.utc).isoformat()
    }

# API endpoints
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(timezone.utc).isoformat(),
        service="crewai-standalone",
        version="1.0.0"
    )

@app.get("/api/health")
async def api_health():
    """API health endpoint for integration hub."""
    return {
        "status": "healthy",
        "service": "crewai-standalone",
        "version": "1.0.0",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "capabilities": [
            "multi_agent_workflows",
            "sequential_execution",
            "parallel_execution",
            "agent_collaboration",
            "task_orchestration"
        ]
    }

@app.post("/api/workflow", response_model=CrewWorkflowResponse)
async def execute_workflow(request: CrewWorkflowRequest):
    """Execute a multi-agent workflow."""
    try:
        logger.info(f"Processing workflow: {request.workflow_name} with {len(request.agents)} agents")
        
        workflow_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        if request.execution_mode == "sequential":
            results = await mock_execute_sequential_workflow(request)
        elif request.execution_mode == "parallel":
            results = await mock_execute_parallel_workflow(request)
        else:
            raise HTTPException(status_code=400, detail=f"Unknown execution mode: {request.execution_mode}")
        
        return CrewWorkflowResponse(
            success=True,
            workflow_id=workflow_id,
            results=results,
            message=f"Workflow '{request.workflow_name}' completed successfully",
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error in workflow execution: {str(e)}")
        return CrewWorkflowResponse(
            success=False,
            workflow_id="",
            message=f"Error: {str(e)}",
            timestamp=datetime.now(timezone.utc).isoformat()
        )

@app.get("/api/capabilities")
async def get_capabilities():
    """Get service capabilities."""
    return {
        "service": "crewai-standalone",
        "capabilities": [
            "multi_agent_workflows",
            "sequential_execution",
            "parallel_execution",
            "agent_collaboration",
            "task_orchestration"
        ],
        "execution_modes": ["sequential", "parallel"],
        "max_agents": 10,
        "max_tasks": 20,
        "version": "1.0.0",
        "status": "operational"
    }

@app.post("/api/create-team")
async def create_agent_team(request: Dict[str, Any]):
    """Create a team of agents."""
    agents_data = request.get("agents", [])
    if not agents_data:
        raise HTTPException(status_code=400, detail="Agents list is required")
    
    agents = [Agent(**agent_data) for agent_data in agents_data]
    team_data = await mock_create_agent_team(agents)
    
    return {
        "success": True,
        "data": team_data,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

@app.post("/api/simple-workflow")
async def simple_workflow(request: Dict[str, Any]):
    """Execute a simple workflow with minimal configuration."""
    task_description = request.get("task")
    if not task_description:
        raise HTTPException(status_code=400, detail="Task description is required")
    
    # Create a simple workflow
    agent = Agent(
        name="GeneralAgent",
        role="Task Executor",
        goal="Complete the given task efficiently",
        backstory="A versatile AI agent capable of handling various tasks"
    )
    
    task = Task(
        description=task_description,
        agent="GeneralAgent",
        expected_output="Completed task result"
    )
    
    workflow = CrewWorkflowRequest(
        workflow_name="SimpleWorkflow",
        agents=[agent],
        tasks=[task],
        execution_mode="sequential"
    )
    
    results = await mock_execute_sequential_workflow(workflow)
    
    return {
        "success": True,
        "data": results,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "CrewAI Standalone Service",
        "version": "1.0.0",
        "status": "running",
        "endpoints": [
            "/health",
            "/api/workflow",
            "/api/capabilities",
            "/api/create-team",
            "/api/simple-workflow",
            "/docs"
        ],
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

if __name__ == "__main__":
    logger.info("Starting CrewAI Standalone Service on port 8004")
    uvicorn.run(
        "crewai_standalone:app",
        host="0.0.0.0",
        port=8004,
        workers=1,
        loop="asyncio"
    )
