# 🔑 API Keys Setup Guide

## Required API Keys

To fully utilize all services in the AI Platform Integration Hub, you'll need to obtain API keys from the following providers:

### 🤖 **OpenAI (Required)**
- **Purpose**: Powers most AI services including Suna, CrewAI, and CopilotKit
- **Get your key**: https://platform.openai.com/api-keys
- **Cost**: Pay-per-use, starts at $0.002/1K tokens
- **Add to .env**: `OPENAI_API_KEY=sk-your-openai-key-here`

### 🧠 **Anthropic Claude (Required)**
- **Purpose**: Alternative LLM provider for Agent-S and advanced reasoning
- **Get your key**: https://console.anthropic.com/
- **Cost**: Pay-per-use, competitive pricing
- **Add to .env**: `ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here`

### 🔍 **Google AI (Optional)**
- **Purpose**: Google's Gemini models and search capabilities
- **Get your key**: https://makersuite.google.com/app/apikey
- **Cost**: Generous free tier, then pay-per-use
- **Add to .env**: `GOOGLE_API_KEY=your-google-api-key-here`

### 🎙️ **LiveKit (Optional)**
- **Purpose**: Real-time voice and video AI interactions
- **Get your keys**: https://cloud.livekit.io/
- **Cost**: Free tier available, then usage-based
- **Add to .env**: 
  ```
  LIVEKIT_API_KEY=your-livekit-api-key
  LIVEKIT_API_SECRET=your-livekit-api-secret
  ```

### 🌐 **Tavily (Optional)**
- **Purpose**: Web search and research capabilities
- **Get your key**: https://tavily.com/
- **Cost**: Free tier available
- **Add to .env**: `TAVILY_API_KEY=your-tavily-api-key`

### 💻 **E2B (Optional)**
- **Purpose**: Secure code execution environment
- **Get your key**: https://e2b.dev/
- **Cost**: Free tier available
- **Add to .env**: `E2B_API_KEY=your-e2b-api-key`

## 📝 How to Add API Keys

1. **Open the .env file** in your project root directory
2. **Replace the placeholder values** with your actual API keys:

```bash
# Example .env configuration
OPENAI_API_KEY=sk-proj-abc123...your-actual-key
ANTHROPIC_API_KEY=sk-ant-api03-xyz789...your-actual-key
GOOGLE_API_KEY=AIzaSy...your-actual-key
```

3. **Save the file** and restart the platform:
```bash
python start_platform.py
```

## 🚀 Quick Start (Minimum Setup)

To get started immediately, you only need:

1. **OpenAI API Key** - Get from https://platform.openai.com/api-keys
2. **Anthropic API Key** - Get from https://console.anthropic.com/

Add these to your `.env` file and you'll have access to:
- ✅ Suna (Web automation & scraping)
- ✅ Agent-S (Desktop automation)
- ✅ CrewAI (Multi-agent workflows)
- ✅ CopilotKit (Frontend AI assistance)

## 💰 Cost Estimates

### Typical Usage Costs (per month):
- **Light usage** (testing, small projects): $5-20
- **Medium usage** (regular development): $20-100
- **Heavy usage** (production applications): $100-500+

### Cost Optimization Tips:
1. Start with smaller models (GPT-3.5, Claude Haiku)
2. Use caching to avoid repeated API calls
3. Monitor usage through provider dashboards
4. Set spending limits on API accounts

## 🔒 Security Best Practices

1. **Never commit API keys to version control**
2. **Use environment variables** (already configured in .env)
3. **Rotate keys regularly** (every 3-6 months)
4. **Set spending limits** on your API accounts
5. **Monitor usage** for unexpected spikes

## 🛠️ Testing Your Setup

After adding your API keys, test the platform:

1. **Visit the dashboard**: http://localhost:8000
2. **Check API docs**: http://localhost:8000/docs
3. **Run health check**: http://localhost:8000/health
4. **Test a simple task** through the API

## 🆘 Troubleshooting

### Common Issues:

**"Invalid API key" errors:**
- Double-check the key format and spelling
- Ensure no extra spaces or characters
- Verify the key is active in the provider dashboard

**"Rate limit exceeded":**
- You've hit the API usage limits
- Wait for the limit to reset or upgrade your plan

**"Insufficient credits":**
- Add billing information to your API account
- Purchase credits or set up auto-billing

## 📞 Support

If you need help:
1. Check the logs: `tail -f ai_platform.log`
2. Visit the health endpoint: http://localhost:8000/health
3. Review the API documentation: http://localhost:8000/docs

## 🎯 Next Steps

Once your API keys are configured:
1. ✅ Test individual services
2. ✅ Create your first multi-agent workflow
3. ✅ Explore the web automation capabilities
4. ✅ Build custom integrations

---

**🎉 You're all set! Your AI Platform Integration Hub is ready to use.**
