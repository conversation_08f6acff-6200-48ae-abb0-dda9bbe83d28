#!/usr/bin/env python3
"""
🤖 CopilotKit Frontend AI Service - Independent Startup
Starts only the CopilotKit frontend AI assistance service.
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from integration_hub.config import Config
from integration_hub.services.copilotkit_service import CopilotKitService
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

def create_copilotkit_app() -> FastAPI:
    """Create FastAPI app for CopilotKit service only."""
    app = FastAPI(
        title="🤖 CopilotKit Frontend AI Service",
        description="Independent frontend AI assistance service",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    return app

async def main():
    """Main function to start CopilotKit service."""
    print("🤖 Starting CopilotKit Frontend AI Service...")
    
    # Load configuration
    config = Config()
    
    # Create FastAPI app
    app = create_copilotkit_app()
    
    # Initialize CopilotKit service with proper parameters
    copilotkit_service = CopilotKitService(
        name="copilotkit",
        url="http://localhost:3000",
        capabilities=["frontend_ai", "code_suggestions", "component_generation"]
    )
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize CopilotKit service on startup."""
        try:
            await copilotkit_service.initialize()
            print("✅ CopilotKit service initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize CopilotKit service: {e}")
            sys.exit(1)
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup CopilotKit service on shutdown."""
        try:
            await copilotkit_service.shutdown()
            print("✅ CopilotKit service shutdown complete")
        except Exception as e:
            print(f"⚠️ Error during CopilotKit shutdown: {e}")
    
    # Add CopilotKit routes
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": "CopilotKit Frontend AI",
            "status": "running",
            "version": "1.0.0",
            "docs": "/docs"
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        try:
            status = await copilotkit_service.health_check()
            return status
        except Exception as e:
            return {"status": "unhealthy", "error": str(e)}
    
    @app.post("/api/suggestions")
    async def get_suggestions(request_data: dict):
        """Get AI suggestions for frontend components."""
        try:
            result = await copilotkit_service.get_suggestions(request_data)
            return result
        except Exception as e:
            return {"error": str(e), "suggestions": []}
    
    @app.post("/api/components/generate")
    async def generate_component(component_data: dict):
        """Generate a frontend component."""
        try:
            result = await copilotkit_service.generate_component(component_data)
            return result
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    @app.get("/api/components/registry")
    async def list_components():
        """List available component templates."""
        try:
            components = await copilotkit_service.list_components()
            return {"components": components}
        except Exception as e:
            return {"error": str(e), "components": []}
    
    @app.get("/api/models/list")
    async def list_models():
        """List available AI models."""
        try:
            models = await copilotkit_service.list_models()
            return {"models": models}
        except Exception as e:
            return {"error": str(e), "models": []}
    
    @app.post("/api/chat")
    async def chat_completion(chat_data: dict):
        """Handle chat completion requests."""
        try:
            result = await copilotkit_service.chat_completion(chat_data)
            return result
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    # Start the server
    port = 3000  # Traditional port for frontend services
    print(f"🚀 Starting CopilotKit service on http://localhost:{port}")
    print(f"📚 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    
    config_dict = {
        "app": app,
        "host": "0.0.0.0",
        "port": port,
        "reload": False,
        "log_level": "info"
    }
    
    await uvicorn.Server(uvicorn.Config(**config_dict)).serve()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 CopilotKit service stopped by user")
    except Exception as e:
        print(f"❌ Failed to start CopilotKit service: {e}")
        sys.exit(1)
