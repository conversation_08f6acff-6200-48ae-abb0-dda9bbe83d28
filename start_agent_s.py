#!/usr/bin/env python3
"""
🖥️ Agent-S Desktop Automation Service - Independent Startup
Starts only the Agent-S desktop automation service.
"""

import os
import sys
import asyncio
import uvicorn
import json
import base64
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from integration_hub.config import Config
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Try to import desktop automation libraries
try:
    import pyautogui
    import psutil
    DESKTOP_AUTOMATION_AVAILABLE = True
except ImportError:
    DESKTOP_AUTOMATION_AVAILABLE = False
    print("⚠️ Desktop automation libraries not available. Install with: pip install pyautogui psutil")

# Data models
class AutomationTask(BaseModel):
    task_type: str
    description: str
    parameters: Dict = {}

class ScreenCaptureRequest(BaseModel):
    region: Optional[Dict] = None  # {"x": 0, "y": 0, "width": 100, "height": 100}
    save_path: Optional[str] = None

class MouseAction(BaseModel):
    action: str  # "click", "double_click", "right_click", "move"
    x: int
    y: int
    button: str = "left"

class KeyboardAction(BaseModel):
    action: str  # "type", "press", "hotkey"
    text: Optional[str] = None
    keys: Optional[List[str]] = None

def create_agent_s_app() -> FastAPI:
    """Create FastAPI app for Agent-S service only."""
    app = FastAPI(
        title="🖥️ Agent-S Desktop Automation Service",
        description="Advanced desktop automation with AI-powered screen understanding",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    return app

# Desktop automation helper functions
async def capture_screenshot(region=None):
    """Capture screenshot of the screen or a specific region."""
    if not DESKTOP_AUTOMATION_AVAILABLE:
        return {"error": "Desktop automation libraries not available"}

    try:
        if region:
            screenshot = pyautogui.screenshot(region=(region['x'], region['y'], region['width'], region['height']))
        else:
            screenshot = pyautogui.screenshot()

        # Save to temporary file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"screenshot_{timestamp}.png"
        filepath = Path("screenshots") / filename
        filepath.parent.mkdir(exist_ok=True)

        screenshot.save(filepath)

        # Convert to base64 for API response
        with open(filepath, "rb") as img_file:
            img_base64 = base64.b64encode(img_file.read()).decode()

        return {
            "success": True,
            "filename": filename,
            "filepath": str(filepath),
            "image_base64": img_base64,
            "timestamp": timestamp,
            "size": {"width": screenshot.width, "height": screenshot.height}
        }
    except Exception as e:
        return {"error": str(e)}

async def get_running_applications():
    """Get list of running applications."""
    if not DESKTOP_AUTOMATION_AVAILABLE:
        return {"error": "Desktop automation libraries not available"}

    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
            try:
                proc_info = proc.info
                if proc_info['name'] and not proc_info['name'].startswith('System'):
                    processes.append({
                        "pid": proc_info['pid'],
                        "name": proc_info['name'],
                        "memory_mb": round(proc_info['memory_info'].rss / 1024 / 1024, 2),
                        "cpu_percent": proc_info['cpu_percent']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        # Sort by memory usage
        processes.sort(key=lambda x: x['memory_mb'], reverse=True)
        return {"applications": processes[:20]}  # Return top 20
    except Exception as e:
        return {"error": str(e)}

async def perform_mouse_action(action: MouseAction):
    """Perform mouse actions."""
    if not DESKTOP_AUTOMATION_AVAILABLE:
        return {"error": "Desktop automation libraries not available"}

    try:
        if action.action == "click":
            pyautogui.click(action.x, action.y, button=action.button)
        elif action.action == "double_click":
            pyautogui.doubleClick(action.x, action.y)
        elif action.action == "right_click":
            pyautogui.rightClick(action.x, action.y)
        elif action.action == "move":
            pyautogui.moveTo(action.x, action.y)
        else:
            return {"error": f"Unknown mouse action: {action.action}"}

        return {"success": True, "action": action.action, "position": {"x": action.x, "y": action.y}}
    except Exception as e:
        return {"error": str(e)}

async def perform_keyboard_action(action: KeyboardAction):
    """Perform keyboard actions."""
    if not DESKTOP_AUTOMATION_AVAILABLE:
        return {"error": "Desktop automation libraries not available"}

    try:
        if action.action == "type" and action.text:
            pyautogui.typewrite(action.text)
        elif action.action == "press" and action.keys:
            for key in action.keys:
                pyautogui.press(key)
        elif action.action == "hotkey" and action.keys:
            pyautogui.hotkey(*action.keys)
        else:
            return {"error": f"Invalid keyboard action or missing parameters"}

        return {"success": True, "action": action.action}
    except Exception as e:
        return {"error": str(e)}

async def main():
    """Main function to start Agent-S service."""
    print("🖥️ Starting Agent-S Desktop Automation Service...")

    # Load configuration
    config = Config()

    # Create FastAPI app
    app = create_agent_s_app()

    # Add Agent-S routes
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": "Agent-S Desktop Automation",
            "status": "running",
            "version": "1.0.0",
            "docs": "/docs",
            "capabilities": ["desktop_automation", "screen_capture", "ui_control"]
        }

    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "service": "agent_s",
            "port": 8002,
            "capabilities": ["desktop_automation", "screen_capture", "ui_control"],
            "desktop_automation_available": DESKTOP_AUTOMATION_AVAILABLE
        }

    @app.post("/api/desktop-automation")
    async def create_desktop_automation_task(task: AutomationTask):
        """Create a desktop automation task."""
        task_id = f"agent_s_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Store task for processing (in a real implementation, this would go to a queue)
        return {
            "task_id": task_id,
            "status": "created",
            "type": task.task_type,
            "description": task.description,
            "parameters": task.parameters,
            "created_at": datetime.now().isoformat(),
            "message": "Agent-S desktop automation task created successfully"
        }

    @app.get("/api/tasks")
    async def list_tasks():
        """List all tasks."""
        # In a real implementation, this would query a database
        return {
            "tasks": [
                {
                    "task_id": "agent_s_task_20240101_120000",
                    "status": "completed",
                    "type": "screen_capture",
                    "description": "Capture desktop screenshot",
                    "created_at": "2024-01-01T12:00:00Z",
                    "completed_at": "2024-01-01T12:00:05Z"
                }
            ]
        }

    @app.post("/api/screen/capture")
    async def capture_screen_endpoint(request: ScreenCaptureRequest = None):
        """Capture current screen or specific region."""
        if request and request.region:
            result = await capture_screenshot(request.region)
        else:
            result = await capture_screenshot()

        return result

    @app.get("/api/screen/capture")
    async def capture_screen_simple():
        """Simple screen capture endpoint."""
        result = await capture_screenshot()
        return result

    @app.get("/api/applications/list")
    async def list_applications():
        """List running applications."""
        result = await get_running_applications()
        return result

    @app.post("/api/mouse/action")
    async def mouse_action_endpoint(action: MouseAction):
        """Perform mouse actions (click, move, etc.)."""
        result = await perform_mouse_action(action)
        return result

    @app.post("/api/keyboard/action")
    async def keyboard_action_endpoint(action: KeyboardAction):
        """Perform keyboard actions (type, press keys, hotkeys)."""
        result = await perform_keyboard_action(action)
        return result

    @app.get("/api/screen/info")
    async def get_screen_info():
        """Get screen information."""
        if not DESKTOP_AUTOMATION_AVAILABLE:
            return {"error": "Desktop automation libraries not available"}

        try:
            screen_size = pyautogui.size()
            mouse_pos = pyautogui.position()
            return {
                "screen_size": {"width": screen_size.width, "height": screen_size.height},
                "mouse_position": {"x": mouse_pos.x, "y": mouse_pos.y},
                "pyautogui_available": True
            }
        except Exception as e:
            return {"error": str(e)}

    @app.get("/api/capabilities")
    async def get_capabilities():
        """Get service capabilities."""
        capabilities = [
            "desktop_automation",
            "screen_capture",
            "ui_control",
            "application_control",
            "mouse_control",
            "keyboard_control"
        ]

        if DESKTOP_AUTOMATION_AVAILABLE:
            capabilities.extend([
                "real_screen_capture",
                "real_mouse_control",
                "real_keyboard_control",
                "process_monitoring"
            ])

        return {
            "capabilities": capabilities,
            "desktop_automation_available": DESKTOP_AUTOMATION_AVAILABLE,
            "libraries": {
                "pyautogui": "pyautogui" in sys.modules,
                "psutil": "psutil" in sys.modules
            }
        }
    
    # Start the server
    port = 8002  # Dedicated port for Agent-S
    print(f"🚀 Starting Agent-S service on http://localhost:{port}")
    print(f"📚 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    
    config_dict = {
        "app": app,
        "host": "0.0.0.0",
        "port": port,
        "reload": False,
        "log_level": "info"
    }
    
    await uvicorn.Server(uvicorn.Config(**config_dict)).serve()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Agent-S service stopped by user")
    except Exception as e:
        print(f"❌ Failed to start Agent-S service: {e}")
        sys.exit(1)
