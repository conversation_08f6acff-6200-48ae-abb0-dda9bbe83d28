#!/usr/bin/env python3
"""
🖥️ Agent-S Desktop Automation Service - Independent Startup
Starts only the Agent-S desktop automation service.
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from integration_hub.config import Config
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

def create_agent_s_app() -> FastAPI:
    """Create FastAPI app for Agent-S service only."""
    app = FastAPI(
        title="🖥️ Agent-S Desktop Automation Service",
        description="Independent desktop automation service",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    return app

async def main():
    """Main function to start Agent-S service."""
    print("🖥️ Starting Agent-S Desktop Automation Service...")

    # Load configuration
    config = Config()

    # Create FastAPI app
    app = create_agent_s_app()

    # Add Agent-S routes
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": "Agent-S Desktop Automation",
            "status": "running",
            "version": "1.0.0",
            "docs": "/docs",
            "capabilities": ["desktop_automation", "screen_capture", "ui_control"]
        }

    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "service": "agent_s",
            "port": 8002,
            "capabilities": ["desktop_automation", "screen_capture", "ui_control"]
        }

    @app.post("/api/desktop-automation")
    async def create_desktop_automation_task(task_data: dict):
        """Create a desktop automation task."""
        # Placeholder implementation
        return {
            "task_id": "agent_s_task_001",
            "status": "created",
            "type": "desktop_automation",
            "data": task_data,
            "message": "Agent-S desktop automation task created successfully"
        }

    @app.get("/api/tasks")
    async def list_tasks():
        """List all tasks."""
        # Placeholder implementation
        return {
            "tasks": [
                {
                    "task_id": "agent_s_task_001",
                    "status": "completed",
                    "type": "desktop_automation",
                    "created_at": "2024-01-01T00:00:00Z"
                }
            ]
        }

    @app.get("/api/screen/capture")
    async def capture_screen():
        """Capture current screen."""
        # Placeholder implementation
        return {
            "screenshot_id": "screen_001",
            "status": "captured",
            "timestamp": "2024-01-01T00:00:00Z",
            "message": "Screen captured successfully"
        }

    @app.get("/api/applications/list")
    async def list_applications():
        """List available applications."""
        # Placeholder implementation
        return {
            "applications": [
                {"name": "Notepad", "pid": 1234, "status": "running"},
                {"name": "Calculator", "pid": 5678, "status": "running"},
                {"name": "Browser", "pid": 9012, "status": "running"}
            ]
        }

    @app.get("/api/capabilities")
    async def get_capabilities():
        """Get service capabilities."""
        return {
            "capabilities": [
                "desktop_automation",
                "screen_capture",
                "ui_control",
                "application_control",
                "mouse_control",
                "keyboard_control"
            ]
        }
    
    # Start the server
    port = 8002  # Dedicated port for Agent-S
    print(f"🚀 Starting Agent-S service on http://localhost:{port}")
    print(f"📚 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    
    config_dict = {
        "app": app,
        "host": "0.0.0.0",
        "port": port,
        "reload": False,
        "log_level": "info"
    }
    
    await uvicorn.Server(uvicorn.Config(**config_dict)).serve()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Agent-S service stopped by user")
    except Exception as e:
        print(f"❌ Failed to start Agent-S service: {e}")
        sys.exit(1)
