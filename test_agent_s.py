#!/usr/bin/env python3
"""
🖥️ Agent-S Desktop Automation Test Client
Test the Agent-S desktop automation capabilities.
"""

import asyncio
import aiohttp
import json
from pathlib import Path

AGENT_S_URL = "http://localhost:8002"

async def test_agent_s():
    """Test Agent-S desktop automation service."""
    print("🖥️ Testing Agent-S Desktop Automation Service")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Health Check
        print("\n1. 🔍 Health Check")
        try:
            async with session.get(f"{AGENT_S_URL}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Service Status: {data['status']}")
                    print(f"📊 Desktop Automation Available: {data.get('desktop_automation_available', False)}")
                else:
                    print(f"❌ Health check failed: {response.status}")
        except Exception as e:
            print(f"❌ Health check error: {e}")
        
        # Test 2: Get Capabilities
        print("\n2. 🛠️ Service Capabilities")
        try:
            async with session.get(f"{AGENT_S_URL}/api/capabilities") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Available Capabilities:")
                    for cap in data['capabilities']:
                        print(f"   • {cap}")
                    print(f"📚 Libraries: {data.get('libraries', {})}")
                else:
                    print(f"❌ Capabilities check failed: {response.status}")
        except Exception as e:
            print(f"❌ Capabilities error: {e}")
        
        # Test 3: Screen Information
        print("\n3. 🖥️ Screen Information")
        try:
            async with session.get(f"{AGENT_S_URL}/api/screen/info") as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' not in data:
                        print(f"✅ Screen Size: {data['screen_size']['width']}x{data['screen_size']['height']}")
                        print(f"🖱️ Mouse Position: ({data['mouse_position']['x']}, {data['mouse_position']['y']})")
                    else:
                        print(f"⚠️ Screen info error: {data['error']}")
                else:
                    print(f"❌ Screen info failed: {response.status}")
        except Exception as e:
            print(f"❌ Screen info error: {e}")
        
        # Test 4: List Running Applications
        print("\n4. 📱 Running Applications")
        try:
            async with session.get(f"{AGENT_S_URL}/api/applications/list") as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' not in data:
                        print("✅ Top Running Applications:")
                        for app in data['applications'][:5]:  # Show top 5
                            print(f"   • {app['name']} (PID: {app['pid']}, Memory: {app['memory_mb']}MB)")
                    else:
                        print(f"⚠️ Applications list error: {data['error']}")
                else:
                    print(f"❌ Applications list failed: {response.status}")
        except Exception as e:
            print(f"❌ Applications list error: {e}")
        
        # Test 5: Screen Capture
        print("\n5. 📸 Screen Capture")
        try:
            async with session.get(f"{AGENT_S_URL}/api/screen/capture") as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' not in data and data.get('success'):
                        print(f"✅ Screenshot captured: {data['filename']}")
                        print(f"📁 Saved to: {data['filepath']}")
                        print(f"📏 Size: {data['size']['width']}x{data['size']['height']}")
                        print(f"🕒 Timestamp: {data['timestamp']}")
                    else:
                        print(f"⚠️ Screen capture error: {data.get('error', 'Unknown error')}")
                else:
                    print(f"❌ Screen capture failed: {response.status}")
        except Exception as e:
            print(f"❌ Screen capture error: {e}")
        
        # Test 6: Create Automation Task
        print("\n6. 🤖 Create Automation Task")
        try:
            task_data = {
                "task_type": "screen_analysis",
                "description": "Analyze current screen content",
                "parameters": {
                    "analyze_ui": True,
                    "extract_text": True
                }
            }
            
            async with session.post(f"{AGENT_S_URL}/api/desktop-automation", 
                                  json=task_data) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Task Created: {data['task_id']}")
                    print(f"📝 Description: {data['description']}")
                    print(f"⏰ Created: {data['created_at']}")
                else:
                    print(f"❌ Task creation failed: {response.status}")
        except Exception as e:
            print(f"❌ Task creation error: {e}")
        
        # Test 7: Mouse Action (Safe - just get current position)
        print("\n7. 🖱️ Mouse Control Test")
        try:
            mouse_data = {
                "action": "move",
                "x": 100,
                "y": 100,
                "button": "left"
            }
            
            async with session.post(f"{AGENT_S_URL}/api/mouse/action", 
                                  json=mouse_data) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' not in data and data.get('success'):
                        print(f"✅ Mouse moved to: ({data['position']['x']}, {data['position']['y']})")
                    else:
                        print(f"⚠️ Mouse action error: {data.get('error', 'Unknown error')}")
                else:
                    print(f"❌ Mouse action failed: {response.status}")
        except Exception as e:
            print(f"❌ Mouse action error: {e}")

async def main():
    """Main test function."""
    print("🚀 Starting Agent-S Desktop Automation Tests...")
    print("⚠️ Make sure Agent-S service is running on http://localhost:8002")
    print()
    
    try:
        await test_agent_s()
        print("\n" + "=" * 50)
        print("✅ Agent-S testing completed!")
        print("🌐 View API docs at: http://localhost:8002/docs")
        print("📊 View dashboard at: http://localhost:8000")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
