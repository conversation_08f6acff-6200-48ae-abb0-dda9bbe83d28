#!/usr/bin/env python3
"""
📊 AI Platform Dashboard - Independent Startup
Starts only the web dashboard for monitoring and controlling AI services.
"""

import os
import sys
import asyncio
import uvicorn
import aiohttp
from pathlib import Path
from typing import Dict, Any

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse

def create_dashboard_app() -> FastAPI:
    """Create FastAPI app for Dashboard only."""
    app = FastAPI(
        title="📊 AI Platform Dashboard",
        description="Independent dashboard for monitoring AI services",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    return app

# Service endpoints mapping
SERVICES = {
    "suna": {"port": 8001, "name": "🌐 Suna Web Automation"},
    "agent_s": {"port": 8002, "name": "🖥️ Agent-S Desktop Automation"},
    "livekit": {"port": 8003, "name": "🎙️ LiveKit Voice/Video AI"},
    "crewai": {"port": 8004, "name": "👥 CrewAI Multi-Agent"},
    "copilotkit": {"port": 3000, "name": "🤖 CopilotKit Frontend AI"}
}

async def check_service_health(service_name: str, port: int) -> Dict[str, Any]:
    """Check if a service is running and healthy."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"http://localhost:{port}/health", timeout=aiohttp.ClientTimeout(total=2)) as response:
                if response.status == 200:
                    data = await response.json()
                    return {"status": "healthy", "port": port, "data": data}
                else:
                    return {"status": "unhealthy", "port": port, "error": f"HTTP {response.status}"}
    except Exception as e:
        return {"status": "offline", "port": port, "error": str(e)}

async def main():
    """Main function to start Dashboard service."""
    print("📊 Starting AI Platform Dashboard...")
    
    # Create FastAPI app
    app = create_dashboard_app()
    
    # Dashboard routes
    @app.get("/", response_class=HTMLResponse)
    async def dashboard():
        """Interactive web dashboard."""
        html_content = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AI Platform Dashboard</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    color: #333;
                }
                .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
                .header { text-align: center; color: white; margin-bottom: 30px; }
                .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
                .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                .card { background: white; border-radius: 15px; padding: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
                .service-card { border-left: 4px solid #667eea; }
                .status-healthy { color: #48bb78; }
                .status-unhealthy { color: #f56565; }
                .status-offline { color: #a0aec0; }
                .btn { background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
                .btn:hover { background: #5a67d8; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 AI Platform Dashboard</h1>
                    <p>Independent Service Monitor & Controller</p>
                </div>
                <div class="grid" id="services-grid">
                    <!-- Services will be loaded here -->
                </div>
            </div>
            <script>
                async function loadServices() {
                    try {
                        const response = await fetch('/api/services/status');
                        const data = await response.json();
                        const grid = document.getElementById('services-grid');
                        
                        grid.innerHTML = Object.entries(data.services).map(([name, service]) => `
                            <div class="card service-card">
                                <h3>${service.name}</h3>
                                <p class="status-${service.status}">Status: ${service.status.toUpperCase()}</p>
                                <p>Port: ${service.port}</p>
                                ${service.status === 'healthy' ? 
                                    `<button class="btn" onclick="window.open('http://localhost:${service.port}/docs', '_blank')">📚 API Docs</button>` :
                                    `<p style="color: #f56565;">Service not running</p>`
                                }
                            </div>
                        `).join('');
                    } catch (error) {
                        console.error('Failed to load services:', error);
                    }
                }
                
                // Load services on page load and refresh every 5 seconds
                loadServices();
                setInterval(loadServices, 5000);
            </script>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content)
    
    @app.get("/api/services/status")
    async def get_services_status():
        """Get status of all AI services."""
        services_status = {}
        
        for service_name, service_info in SERVICES.items():
            status = await check_service_health(service_name, service_info["port"])
            services_status[service_name] = {
                "name": service_info["name"],
                "port": service_info["port"],
                "status": status["status"],
                "error": status.get("error"),
                "data": status.get("data")
            }
        
        return {"services": services_status}
    
    @app.get("/health")
    async def health_check():
        """Dashboard health check."""
        return {"status": "healthy", "service": "dashboard"}
    
    # Start the server
    port = 8000  # Main dashboard port
    print(f"🚀 Starting Dashboard on http://localhost:{port}")
    print(f"📚 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    print("\n📋 Available Services:")
    for service_name, service_info in SERVICES.items():
        print(f"  • {service_info['name']}: http://localhost:{service_info['port']}")
    
    config_dict = {
        "app": app,
        "host": "0.0.0.0",
        "port": port,
        "reload": False,
        "log_level": "info"
    }
    
    await uvicorn.Server(uvicorn.Config(**config_dict)).serve()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Failed to start Dashboard: {e}")
        sys.exit(1)
