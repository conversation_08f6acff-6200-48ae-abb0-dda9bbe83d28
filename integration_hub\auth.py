"""
Authentication and authorization module for the AI Platform Integration Hub.

This module provides authentication, session management, and authorization
functionality for the platform.
"""

import jwt
import hashlib
import secrets
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from .models import UserSession

logger = logging.getLogger(__name__)


@dataclass
class AuthConfig:
    """Authentication configuration."""
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    max_sessions_per_user: int = 5


class AuthManager:
    """Manages authentication and authorization."""
    
    def __init__(self, config: AuthConfig):
        self.config = config
        self.active_sessions: Dict[str, UserSession] = {}
        self.user_credentials: Dict[str, str] = {}  # username -> hashed_password
        self.refresh_tokens: Dict[str, str] = {}  # refresh_token -> user_id
        
    async def create_user(self, username: str, password: str, roles: List[str] = None) -> bool:
        """Create a new user."""
        try:
            if username in self.user_credentials:
                return False
                
            hashed_password = self._hash_password(password)
            self.user_credentials[username] = hashed_password
            
            logger.info(f"User created: {username}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating user {username}: {e}")
            return False
            
    async def authenticate_user(self, username: str, password: str) -> Optional[str]:
        """Authenticate a user and return user ID if successful."""
        try:
            if username not in self.user_credentials:
                return None
                
            stored_hash = self.user_credentials[username]
            if not self._verify_password(password, stored_hash):
                return None
                
            return username  # Using username as user_id for simplicity
            
        except Exception as e:
            logger.error(f"Error authenticating user {username}: {e}")
            return None
            
    async def create_session(self, user_id: str, roles: List[str] = None, 
                           permissions: List[str] = None) -> Tuple[str, str]:
        """Create a new session and return access and refresh tokens."""
        try:
            # Clean up old sessions for this user
            await self._cleanup_user_sessions(user_id)
            
            # Create session
            session = UserSession(
                user_id=user_id,
                roles=roles or ["user"],
                permissions=permissions or ["read", "write"],
                created_at=datetime.now(timezone.utc),
                last_activity=datetime.now(timezone.utc),
                expires_at=datetime.now(timezone.utc) + timedelta(
                    minutes=self.config.access_token_expire_minutes
                )
            )
            
            # Generate tokens
            access_token = self._generate_access_token(session)
            refresh_token = self._generate_refresh_token(user_id)
            
            # Store session
            self.active_sessions[session.session_id] = session
            self.refresh_tokens[refresh_token] = user_id
            
            logger.info(f"Session created for user: {user_id}")
            return access_token, refresh_token
            
        except Exception as e:
            logger.error(f"Error creating session for user {user_id}: {e}")
            raise
            
    async def validate_token(self, token: str) -> Optional[UserSession]:
        """Validate an access token and return the session."""
        try:
            # Decode token
            payload = jwt.decode(
                token, 
                self.config.secret_key, 
                algorithms=[self.config.algorithm]
            )
            
            session_id = payload.get("session_id")
            if not session_id:
                return None
                
            # Get session
            session = self.active_sessions.get(session_id)
            if not session:
                return None
                
            # Check if session is expired
            if session.expires_at < datetime.now(timezone.utc):
                await self._remove_session(session_id)
                return None
                
            # Update last activity
            session.last_activity = datetime.now(timezone.utc)
            
            return session
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token expired")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
        except Exception as e:
            logger.error(f"Error validating token: {e}")
            return None
            
    async def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """Refresh an access token using a refresh token."""
        try:
            user_id = self.refresh_tokens.get(refresh_token)
            if not user_id:
                return None
                
            # Find active session for user
            user_sessions = [s for s in self.active_sessions.values() 
                           if s.user_id == user_id and s.is_active]
            
            if not user_sessions:
                return None
                
            session = user_sessions[0]  # Use the first active session
            
            # Extend session expiry
            session.expires_at = datetime.now(timezone.utc) + timedelta(
                minutes=self.config.access_token_expire_minutes
            )
            session.last_activity = datetime.now(timezone.utc)
            
            # Generate new access token
            access_token = self._generate_access_token(session)
            
            logger.info(f"Access token refreshed for user: {user_id}")
            return access_token
            
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            return None
            
    async def revoke_session(self, session_id: str) -> bool:
        """Revoke a session."""
        try:
            return await self._remove_session(session_id)
        except Exception as e:
            logger.error(f"Error revoking session {session_id}: {e}")
            return False
            
    async def check_permission(self, session: UserSession, permission: str) -> bool:
        """Check if a session has a specific permission."""
        return permission in session.permissions or "admin" in session.roles
        
    async def get_active_sessions(self, user_id: Optional[str] = None) -> List[UserSession]:
        """Get active sessions, optionally filtered by user."""
        sessions = list(self.active_sessions.values())
        
        if user_id:
            sessions = [s for s in sessions if s.user_id == user_id]
            
        # Filter out expired sessions
        now = datetime.now(timezone.utc)
        active_sessions = [s for s in sessions if s.expires_at > now and s.is_active]
        
        return active_sessions
        
    def _hash_password(self, password: str) -> str:
        """Hash a password using SHA-256 with salt."""
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"{salt}:{password_hash}"
        
    def _verify_password(self, password: str, stored_hash: str) -> bool:
        """Verify a password against a stored hash."""
        try:
            salt, password_hash = stored_hash.split(":")
            computed_hash = hashlib.sha256((password + salt).encode()).hexdigest()
            return computed_hash == password_hash
        except ValueError:
            return False
            
    def _generate_access_token(self, session: UserSession) -> str:
        """Generate an access token for a session."""
        payload = {
            "session_id": session.session_id,
            "user_id": session.user_id,
            "roles": session.roles,
            "permissions": session.permissions,
            "exp": session.expires_at,
            "iat": datetime.now(timezone.utc)
        }
        
        return jwt.encode(payload, self.config.secret_key, algorithm=self.config.algorithm)
        
    def _generate_refresh_token(self, user_id: str) -> str:
        """Generate a refresh token."""
        return secrets.token_urlsafe(32)
        
    async def _remove_session(self, session_id: str) -> bool:
        """Remove a session."""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.is_active = False
            del self.active_sessions[session_id]
            
            # Remove associated refresh tokens
            refresh_tokens_to_remove = [
                token for token, uid in self.refresh_tokens.items() 
                if uid == session.user_id
            ]
            for token in refresh_tokens_to_remove:
                del self.refresh_tokens[token]
                
            logger.info(f"Session removed: {session_id}")
            return True
            
        return False
        
    async def _cleanup_user_sessions(self, user_id: str) -> None:
        """Clean up old sessions for a user."""
        user_sessions = [s for s in self.active_sessions.values() if s.user_id == user_id]
        
        # Sort by creation time, keep only the most recent ones
        user_sessions.sort(key=lambda s: s.created_at, reverse=True)
        
        sessions_to_remove = user_sessions[self.config.max_sessions_per_user - 1:]
        
        for session in sessions_to_remove:
            await self._remove_session(session.session_id)
            
    async def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions and return count of removed sessions."""
        now = datetime.now(timezone.utc)
        expired_sessions = [
            s for s in self.active_sessions.values() 
            if s.expires_at <= now
        ]
        
        for session in expired_sessions:
            await self._remove_session(session.session_id)
            
        logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
        return len(expired_sessions)
