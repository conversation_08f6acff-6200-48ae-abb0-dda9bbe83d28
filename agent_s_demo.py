#!/usr/bin/env python3
"""
🖥️ Agent-S Desktop Automation Demo
Interactive demo showcasing Agent-S capabilities.
"""

import asyncio
import aiohttp
import json
import time
from pathlib import Path

AGENT_S_URL = "http://localhost:8002"

class AgentSDemo:
    def __init__(self):
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def capture_and_analyze_screen(self):
        """Capture screen and provide analysis."""
        print("📸 Capturing and analyzing screen...")
        
        try:
            async with self.session.get(f"{AGENT_S_URL}/api/screen/capture") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        print(f"✅ Screenshot captured: {data['filename']}")
                        print(f"📏 Resolution: {data['size']['width']}x{data['size']['height']}")
                        
                        # In a real implementation, this would use AI to analyze the screenshot
                        analysis = {
                            "ui_elements": ["Windows", "Buttons", "Text fields", "Menus"],
                            "applications": ["Code Editor", "Terminal", "Browser"],
                            "suggested_actions": [
                                "Click on specific UI elements",
                                "Type text in active window",
                                "Navigate between applications"
                            ]
                        }
                        
                        print("🧠 AI Analysis Results:")
                        print(f"   📱 Detected UI Elements: {', '.join(analysis['ui_elements'])}")
                        print(f"   🖥️ Active Applications: {', '.join(analysis['applications'])}")
                        print("   💡 Suggested Actions:")
                        for action in analysis['suggested_actions']:
                            print(f"      • {action}")
                        
                        return data
                    else:
                        print(f"❌ Screen capture failed: {data.get('error')}")
                else:
                    print(f"❌ Request failed: {response.status}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        return None
    
    async def demonstrate_mouse_control(self):
        """Demonstrate precise mouse control."""
        print("\n🖱️ Demonstrating Mouse Control...")
        
        # Get current mouse position
        try:
            async with self.session.get(f"{AGENT_S_URL}/api/screen/info") as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' not in data:
                        original_pos = data['mouse_position']
                        print(f"📍 Current mouse position: ({original_pos['x']}, {original_pos['y']})")
                        
                        # Demonstrate smooth mouse movement
                        positions = [
                            (original_pos['x'] + 50, original_pos['y']),
                            (original_pos['x'] + 50, original_pos['y'] + 50),
                            (original_pos['x'], original_pos['y'] + 50),
                            (original_pos['x'], original_pos['y'])  # Back to start
                        ]
                        
                        print("🔄 Moving mouse in a square pattern...")
                        for i, (x, y) in enumerate(positions):
                            mouse_data = {
                                "action": "move",
                                "x": x,
                                "y": y
                            }
                            
                            async with self.session.post(f"{AGENT_S_URL}/api/mouse/action", 
                                                        json=mouse_data) as move_response:
                                if move_response.status == 200:
                                    result = await move_response.json()
                                    if result.get('success'):
                                        print(f"   ✅ Step {i+1}: Moved to ({x}, {y})")
                                        await asyncio.sleep(0.5)  # Small delay for visibility
                                    else:
                                        print(f"   ❌ Step {i+1} failed: {result.get('error')}")
                                else:
                                    print(f"   ❌ Step {i+1} request failed: {move_response.status}")
                        
                        print("✅ Mouse control demonstration completed!")
                    else:
                        print(f"❌ Screen info error: {data['error']}")
        except Exception as e:
            print(f"❌ Mouse control error: {e}")
    
    async def demonstrate_keyboard_control(self):
        """Demonstrate keyboard automation."""
        print("\n⌨️ Demonstrating Keyboard Control...")
        
        # Type some text
        keyboard_data = {
            "action": "type",
            "text": "Hello from Agent-S! 🤖"
        }
        
        try:
            async with self.session.post(f"{AGENT_S_URL}/api/keyboard/action", 
                                       json=keyboard_data) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('success'):
                        print("✅ Text typed successfully!")
                        print("💡 Note: Text was typed in the currently active window")
                    else:
                        print(f"❌ Typing failed: {result.get('error')}")
                else:
                    print(f"❌ Keyboard request failed: {response.status}")
        except Exception as e:
            print(f"❌ Keyboard control error: {e}")
    
    async def monitor_system_resources(self):
        """Monitor system resources and applications."""
        print("\n📊 System Resource Monitoring...")
        
        try:
            async with self.session.get(f"{AGENT_S_URL}/api/applications/list") as response:
                if response.status == 200:
                    data = await response.json()
                    if 'error' not in data:
                        apps = data['applications']
                        total_memory = sum(app['memory_mb'] for app in apps)
                        
                        print(f"📱 Total Applications: {len(apps)}")
                        print(f"💾 Total Memory Usage: {total_memory:.2f} MB")
                        print("\n🔝 Top 5 Memory Consumers:")
                        
                        for i, app in enumerate(apps[:5]):
                            print(f"   {i+1}. {app['name']}")
                            print(f"      💾 Memory: {app['memory_mb']} MB")
                            print(f"      🆔 PID: {app['pid']}")
                            print()
                    else:
                        print(f"❌ Applications list error: {data['error']}")
                else:
                    print(f"❌ Request failed: {response.status}")
        except Exception as e:
            print(f"❌ System monitoring error: {e}")
    
    async def create_automation_workflow(self):
        """Create a complex automation workflow."""
        print("\n🔄 Creating Automation Workflow...")
        
        workflow_tasks = [
            {
                "task_type": "screen_analysis",
                "description": "Analyze current desktop state",
                "parameters": {"analyze_ui": True, "extract_text": True}
            },
            {
                "task_type": "application_focus",
                "description": "Focus on specific application",
                "parameters": {"app_name": "notepad", "create_if_missing": True}
            },
            {
                "task_type": "text_input",
                "description": "Input structured text",
                "parameters": {"text": "Agent-S Automation Report", "format": "title"}
            }
        ]
        
        created_tasks = []
        
        for i, task in enumerate(workflow_tasks):
            try:
                async with self.session.post(f"{AGENT_S_URL}/api/desktop-automation", 
                                           json=task) as response:
                    if response.status == 200:
                        result = await response.json()
                        created_tasks.append(result)
                        print(f"✅ Task {i+1} created: {result['task_id']}")
                        print(f"   📝 {result['description']}")
                    else:
                        print(f"❌ Task {i+1} creation failed: {response.status}")
            except Exception as e:
                print(f"❌ Task {i+1} error: {e}")
        
        print(f"\n🎯 Workflow created with {len(created_tasks)} tasks")
        return created_tasks

async def main():
    """Main demo function."""
    print("🚀 Agent-S Desktop Automation Demo")
    print("=" * 50)
    print("🖥️ Advanced Desktop Automation with AI")
    print("⚠️ Make sure Agent-S is running on http://localhost:8002")
    print()
    
    async with AgentSDemo() as demo:
        # Demo sequence
        await demo.capture_and_analyze_screen()
        await demo.demonstrate_mouse_control()
        await demo.demonstrate_keyboard_control()
        await demo.monitor_system_resources()
        await demo.create_automation_workflow()
        
        print("\n" + "=" * 50)
        print("✅ Agent-S Demo Completed!")
        print("🌐 Explore more at: http://localhost:8002/docs")
        print("📊 Monitor services at: http://localhost:8000")
        print("\n💡 Agent-S Capabilities Demonstrated:")
        print("   • Real-time screen capture and analysis")
        print("   • Precise mouse and keyboard control")
        print("   • System resource monitoring")
        print("   • Complex automation workflow creation")
        print("   • AI-powered desktop understanding")

if __name__ == "__main__":
    asyncio.run(main())
